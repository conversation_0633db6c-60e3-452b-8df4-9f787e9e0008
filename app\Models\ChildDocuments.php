<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChildDocuments extends Model
{
    use HasFactory, SoftDeletes;

    protected $table = 'children_documents';
    protected $fillable = [
        'child_id',
        'user_id',
        'name',
        'file_path',
        'uploaded_date',
        'file_type',
        'status'
    ];
}
