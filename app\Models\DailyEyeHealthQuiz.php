<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DailyEyeHealthQuiz extends Model
{
    use HasFactory;

    protected $table = 'daily_eye_health_reports';
    protected $fillable = [
        'user_id',
        'child_id',
        'breaks_every_20_min',
        'focus_20_feet',
        'one_hour_outdoor',
        'outdoor_activity',
        'screen_time_limit',
        'no_screen_meals_bed',
        'eye_exercises',
        'distance_viewing',
        'eye_healthy_food',
        'hydrated',
        'total_points',
        'percentage',
    ];
    /**
     * Get the child that owns the quiz.
     */
    public function child()
    {
        return $this->belongsTo(Childs::class);
    }

    /**
     * Get the user who owns the quiz through the child.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
