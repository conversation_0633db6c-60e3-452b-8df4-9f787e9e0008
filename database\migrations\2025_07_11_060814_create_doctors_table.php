<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('doctors', function (Blueprint $table) {
            $table->id();
            $table->string('name')->nullable(); 
            $table->string('specialty')->nullable(); 
            $table->integer('experience')->nullable(); 
            $table->string('image_key')->nullable(); 
            $table->decimal('fees', 8, 2)->nullable();
            $table->decimal('ratings', 3, 2)->nullable(); 
            $table->text('availability_slots')->nullable();
            $table->tinyInteger('status')->default(1)->comment('1=>active, 0=>inactive');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('doctors');
    }
};
