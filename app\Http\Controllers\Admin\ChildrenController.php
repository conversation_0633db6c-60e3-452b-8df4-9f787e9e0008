<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ChildDocuments;
use App\Models\Childs;
use App\Models\DailyEyeHealthQuiz;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Kreait\Firebase\Factory;

class ChildrenController extends Controller
{
    // ****************Show all children of a specific user****************
    public function index()
    {
        try {
            $children = Childs::all();
            return view('admin.children.index', compact('user', 'children'));
        } catch (\Exception $e) {
            Log::error('Error fetching children list: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }
    // ****************Show all children of a specific user****************

    // ****************Show a specific child****************
    public function show($user_id, $child_id)
    {
        try {
            $child = Childs::where('user_id', $user_id)->with(['badges', 'myopiaAssessments', 'documents'])->findOrFail($child_id);

            return view('admin.users.modal-view', compact('child'));
        } catch (\Exception $e) {
            Log::error('Error fetching child details: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong. Please try again.'], 500);
        }
    }
    // ****************Show a specific child****************

    // ****************Show Daily Quiz Report****************
    public function dailyQuizReport($user_id, $child_id)
    {
        try {
            $dailyQuizReport = DailyEyeHealthQuiz::where('user_id', $user_id)->where('child_id', $child_id)->get();
            return view('admin.users.modal-report-view', compact('dailyQuizReport'));
        } catch (\Exception $e) {
            Log::error('Error fetching child details: ' . $e->getMessage());
            return response()->json(['error' => 'Something went wrong. Please try again.'], 500);
        }
    }
    // ****************Show Daily Quiz Report****************

    // ****************Show form to edit child****************
    public function edit($user_id, $child_id)
    {
        try {
            $children = Childs::where('user_id', $user_id)->findOrFail($child_id);
            return view('admin.children.edit', compact('children'));
        } catch (\Exception $e) {
            Log::error('Error fetching child for editing: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }
    // ****************Show form to edit child****************

    // ****************Update child data****************
    public function update(Request $request, $user_id, $child_id)
    {
        try {
            $children = Childs::where('user_id', $user_id)->findOrFail($child_id);
            $request->validate([
                'name_of_kid' => 'required|string|max:255',
                'age' => 'required|integer',
                'school' => 'nullable|string|max:255',
                'existing_eye_power' => 'nullable|string|max:50',
                'status' => 'required|in:1,0',
            ]);

            $children->update([
                'name_of_kid' => $request->name_of_kid,
                'age' => $request->age,
                'school' => $request->school,
                'existing_eye_power' => $request->existing_eye_power,
                'status' => $request->status,
            ]);

            if ($request->hasFile('kid_image')) {
                $imagePath = $request->file('kid_image')->store('children_images', 'public');
                $children->kid_image = $imagePath;
                $children->save();
            }
            return response()->json(['success' => true, 'message' => 'Updated'], 200);
        } catch (ValidationException $e) {
            return redirect()->back()->withErrors($e->errors())->withInput();
        } catch (\Exception $e) {
            Log::error('Error updating child: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again.');
        }
    }
    // ****************Update child data****************

    // ****************Delete child record****************
    public function destroy($user_id, $child_id)
    {
        try {
            $child = Childs::where('user_id', $user_id)->findOrFail($child_id);
            $child->delete();
            return response()->json(['success' => true, 'message' => 'Child deleted successfully.']);
        } catch (\Exception $e) {
            Log::error('Error deleting child: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong.'], 500);
        }
    }
    // ****************Delete child record****************

    // *********Child-Status-Change*********
    public function toggleChildrenStatus(Request $request, $user_id, $child_id)
    {
        try {
            $request->validate([
                'status' => 'required',
            ]);
            $child = Childs::where('id', $child_id)->where('user_id', $user_id)->firstOrFail();
            $child->status = $request->status;
            $child->save();
            return response()->json(['success' => true, 'message' => 'Status updated successfully.']);
        } catch (Exception $e) {
            Log::error('Error updating child status: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again.'], 500);
        }
    }
    // *********Child-Status-Change*********

    // *********Child-Documents-Upload*********
    public function uploadFile(Request $request, $user_id, $child_id)
    {
        try {
            $request->validate([
                'file' => 'required|file',
            ]);

            $file = $request->file('file');
            $fileName = 'children_documents/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $serviceAccountFile = base_path('firebase-credential.json');
            $firebase = (new \Kreait\Firebase\Factory())->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');
            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );
            $fileUrl = "https://firebasestorage.googleapis.com/v0/b/" . $bucket->name() . "/o/" . urlencode($fileName) . "?alt=media";

            ChildDocuments::create([
                'child_id' => $child_id,
                'user_id' => $user_id,
                'name' => $fileName,
                'file_path' => $fileUrl,
                'uploaded_date' => now()->format('Y-m-d'),
                'file_type' => $file->getClientOriginalExtension(),
                'status' => 1,
            ]);
            return response()->json(['success' => true, 'message' => 'File uploaded successfully.', 'file_url' => $fileUrl]);
        } catch (\Exception $e) {
            Log::error('File upload failed', ['error' => $e->getMessage(), 'trace' => $e->getTraceAsString()]);
            return response()->json(['success' => false, 'message' => 'File upload failed: ' . $e->getMessage()], 500);
        }
    }
    // *********Child-Documents-Upload*********

    // *********Child-Document-Delete*********
    public function documentDestroy($user_id, $child_id, $document_id)
    {
        try {
            $document = ChildDocuments::where('user_id', $user_id)->where('child_id', $child_id)->findOrFail($document_id);
            $document->delete();

            return redirect()->back()->with('success', 'Document deleted successfully.');
        } catch (\Exception $e) {
            Log::error('Error deleting document: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong while deleting the document.');
        }
    }
// *********Child-Document-Delete*********

}
