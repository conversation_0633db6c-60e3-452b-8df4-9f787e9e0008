<?php

use App\Http\Controllers\Admin\Auth\AuthController;
use App\Http\Controllers\Admin\ChildrenController;
use App\Http\Controllers\Admin\UserController;
use App\Http\Controllers\Admin\WebSettingController;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/clear-cache', function () {
	Artisan::call('config:clear');
	Artisan::call('cache:clear');
	Artisan::call('config:cache');
	Artisan::call('optimize:clear');
	echo "config cleared";
});

// Route::get('/', function () {
//     return view('landing');
// })->name('landing');

// Route::prefix('admin')->group(function () {
    // Admin Login/Logout
    Route::get('/',  [AuthController::class, 'showLoginForm'])->name('admin.login.form');
    Route::post('/login',  [AuthController::class, 'login'])->name('admin.login');
    Route::get('/logout',  [AuthController::class, 'logout'])->name('admin.logout');

    Route::middleware('auth:admin')->group(function () {
        Route::get('/dashboard', [AuthController::class, 'dashboard'])->name('dashboard');

        // Parent User Routes
        Route::get('/user/index', [UserController::class, 'userIndex'])->name('userIndex');
        Route::post('/user/change-status', [UserController::class, 'toggleUserStatus'])->name('toggleUserStatus');
        Route::get('/user/view/{id}', [UserController::class, 'userView'])->name('userView');
        Route::get('/user/edit/{id}', [UserController::class, 'userEdit'])->name('userEdit');
        Route::put('/user/update/{id}', [UserController::class, 'userUpdate'])->name('userUpdate');
        Route::delete('/user/delete/{id}', [UserController::class, 'userdelete'])->name('userdelete');

        // Child (Children) CRUD Routes
        Route::prefix('/user/{user_id}/children')->group(function () {
            Route::get('/', [ChildrenController::class, 'index'])->name('children.index');
            Route::get('/{child_id}', [ChildrenController::class, 'show'])->name('children.show');
            Route::get('/daily-quiz-report/{child_id}', [ChildrenController::class, 'dailyQuizReport'])->name('children.dailyQuizReport');
            Route::post('/{child_id}/upload-file', [ChildrenController::class, 'uploadFile'])->name('children.uploadFile');
            Route::get('/{child_id}/edit', [ChildrenController::class, 'edit'])->name('children.edit');
            Route::post('/{child_id}/change-status', [ChildrenController::class, 'toggleChildrenStatus'])->name('toggleChildrenStatus');
            Route::put('/{child_id}', [ChildrenController::class, 'update'])->name('children.update');
            Route::delete('/{child_id}', [ChildrenController::class, 'destroy'])->name('children.destroy');
            Route::delete('/{child_id}/document/{document_id}', [ChildrenController::class, 'documentDestroy'])->name('document.destroy');
        });

        Route::get('/web-setting/tnc', [WebSettingController::class, 'termsNcondition'])->name('termsNcondition');
        Route::get('/web-setting/privacy-policy', [WebSettingController::class, 'privacyPolicy'])->name('privacyPolicy');
        Route::put('/web-setting/update/{id}', [WebSettingController::class, 'updateWebSettings'])->name('updateWebSettings.update');

        // Push Notification
        Route::get('/send-notification/view', [WebSettingController::class, 'pushNotificationShow'])->name('sendNotification');
        Route::post('/send-notification/store', [WebSettingController::class, 'pushNotificationStore'])->name('sendNotification.store');
        Route::delete('/send-notification/delete/{id}', [WebSettingController::class, 'pushNotificationDestroy'])->name('sendNotification.destroy');
        Route::post('/send-notification/bulk-delete', [WebSettingController::class, 'pushNotificationBulkDelete'])->name('sendNotification.bulkDelete');
        // Push Notification
    });
// });