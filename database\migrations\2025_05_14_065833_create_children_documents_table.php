<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateChildrenDocumentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('children_documents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('child_id');
            $table->unsignedBigInteger('user_id');
            $table->string('name')->nullable();
            $table->string('file_path')->nullable();
            $table->string('uploaded_date')->nullable(); 
            $table->string('file_type')->nullable();
            $table->boolean('status')->default(1);
            $table->timestamps();
            $table->softDeletes(); 

            $table->foreign('child_id')->references('id')->on('childrens')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('children_documents');
    }
}
