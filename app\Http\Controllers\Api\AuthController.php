<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\User;
use Exception;
use Firebase\JWT\JWT;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Exception\Auth\UserNotFound;
use Kreait\Firebase\Factory;

class AuthController extends Controller
{
    // **********LOGIN-USER**********
    public function login(Request $request)
    {
        $request->validate([
            'mobile' => 'required',
            'firebase_token' => 'required',
            // 'fcm_token' => 'required',
        ]);
        try {
            $user = User::where('mobile', $request->mobile)->first();
            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not found'], 200);
            }
            if (!$this->verify_user($request->firebase_token)) {
                return response()->json(['success' => false, 'message' => 'Invalid Firebase credential'], 400);
            }
            $token = $this->generate_token($user->id, $request->firebase_token);
            $user->update(['api_token' => $token]);
            return response()->json(['success' => true, 'message' => 'Login successful', 'token' => $token, 'user' => $user], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error logging in'], 500);
        }
    }
    // **********LOGIN-USER**********

    // **********ADD-PARENT-DETAILS**********
    public function createUserProfile(Request $request)
    {
        $request->validate([
            'mobile' => 'required',
            'full_name' => 'nullable',
            'email' => 'nullable|email',
            'occupation' => 'nullable|string',
            'firebase_token' => 'required',
            // 'fcm_token' => 'required',
        ]);

        try {
            // If user doesn't exist
            $userImageUrl = $request->hasFile('user_image') ? $this->uploadImageToFirebase($request->file('user_image')) : url('default-img.png');
            $user = User::updateOrCreate(
                ['mobile' => $request->mobile],
                [
                    'full_name'   => $request->full_name,
                    'email'       => $request->email,
                    'occupation'  => $request->occupation,
                    'firebase_id' => $request->firebase_token,
                    // 'fcm_id'      => $request->fcm_token,
                    'user_image'  => $userImageUrl,
                ]
            );

            // Generate and update API token
            $token = $this->generate_token($user->id, $request->firebase_token);
            $user->api_token = $token;
            $user->save();
            return response()->json(['success' => true, 'message' => 'Profile created and registration completed successfully', 'token' => $token, 'user' => $user], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false,'message' => 'Error creating profile'], 500);
        }
    }
    // **********ADD-PARENT-DETAILS**********

    // **********UPLOAD-IMAGE-TO-FIREBASE**********
    private function uploadImageToFirebase($file)
    {
        try {
            $serviceAccountFile = base_path('firebase-credential.json');
            $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');
            $fileName = 'user_image/' . uniqid() . '.' . $file->getClientOriginalExtension();
            // dd($serviceAccountFile);
            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );
            return "https://firebasestorage.googleapis.com/v0/b/" . $bucket->name() . "/o/" . urlencode($fileName) . "?alt=media";
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return url('default-img.png');
        }
    }
    // **********UPLOAD-IMAGE-TO-FIREBASE**********

    // *********USER-FIREBASE-VERIFICATION*********
    public function verify_user($firebase_token)
    {
        $firebaseConfigPath = base_path('firebase-credential.json');
        if (file_exists($firebaseConfigPath)) {
            $factory = app(Factory::class)->withServiceAccount($firebaseConfigPath);
            $firebaseAuth = $factory->createAuth();
            try {
                $decodedToken = $firebaseAuth->verifyIdToken($firebase_token);
                $uid = $decodedToken->claims()->get('sub');
                if ($uid) {
                    return true;
                } else {
                    return false;
                }
            } catch (UserNotFound $e) {
                Log::error($e->getMessage());
                return false;
            }
        } else {
            return false;
        }
    }
    // *********USER-FIREBASE-VERIFICATION*********

    // *********GENERATE-TOKEN*********
    public function generate_token($user_id, $firebase_token)
    {
        $key = env('JWT_SECRET_KEY');
        $payload = [
            'iat' => time(),
            'exp' => time() + (30 * 60 * 60 * 24),
            'user_id' => $user_id,
            'firebase_id' => $firebase_token,
            'sub' => 'Eyefit',
        ];
        return JWT::encode($payload, $key, 'HS256');
    }
    // *********GENERATE-TOKEN*********
}
