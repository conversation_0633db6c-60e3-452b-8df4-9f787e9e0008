{{-- Child-Details-Section --}}
<h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Child Details 📋</h2>
<div class="row p-3">
    <div class="col-md-4 text-center">
        @php
        $hasScreenTimeNinja = $child->badges->contains('name', 'Screen Time Ninja');
        @endphp
        <img src="{{ $child->kid_image ? asset($child->kid_image) : asset('assets/img/eyenstein.png') }}" alt="Child Image" class="img-fluid rounded-circle p-3 {{ $hasScreenTimeNinja ? 'streak-glow' : '' }}" style="width: 150px; height: 150px; object-fit: cover; border: 3px solid #ddd;">
    </div>
    <div class="col-md-8">
        <h4 class="text-dark fw-bold mb-3">{{ $child->name_of_kid }}<span class="badge badge-lg {{ $child->status == 1 ? 'badge-light-success' : 'badge-light-danger' }} p-2">{{ $child->status == 1 ? 'Active Child' : 'Inactive Child' }}</span></h4>
        <div class="row mb-2">
            <div class="col-5"><span class="fw-bold text-dark">Age:</span></div>
            <div class="col-7 text-dark">{{ $child->age }}</div>
        </div>
        <div class="row mb-2">
            <div class="col-5"><span class="fw-bold text-dark">School:</span></div>
            <div class="col-7 text-dark">{{ $child->school }}</div>
        </div>
        <div class="row mb-2">
            <div class="col-5"><span class="fw-bold text-dark">Existing Eye Power:</span></div>
            <div class="col-7 text-dark">{{ $child->existing_eye_power }} Eye Power</div>
        </div>
    </div>
</div>
{{-- Child-Details-Section-End --}}

{{-- Myopia-Quiz-Result-&-Achievements-Side-by-Side --}}
<div class="row p-3">
    {{-- Myopia-Quiz-Result-Section --}}
    <div class="col-md-6">
        <h2 class="px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Myopia Quiz Assessments 📊</h2>
        <div class="row mt-5">
            @if($child->myopiaAssessments)
            <div class="col-md-12 mb-3 d-flex">
                <div class="card shadow-sm border-0 h-100 w-100 d-flex flex-column">
                    <div class="card-body text-center d-flex flex-column">
                        <i class="fas fa-chart-bar text-info fa-3x"></i>
                        <h5 class="mt-3">Total Points: {{ $child->myopiaAssessments->total_points }}</h5>
                        <p class="text-muted flex-grow-1">
                            <strong>Risk Level:</strong><span class="@if($child->myopiaAssessments->risk_level == 'Mid') text-success @elseif($child->myopiaAssessments->risk_level == 'Moderate') text-warning @elseif($child->myopiaAssessments->risk_level == 'Severe') text-danger @else text-dark @endif"> {{ $child->myopiaAssessments->risk_level }}</span>
                        </p>
                        <p class="text-muted flex-grow-1">"These points and risk level is calculated based on the Myopia Risk Assessments Quiz."</p>
                    </div>
                </div>
            </div>
            @else
            <div class="col-md-12 text-center">
                <p class="text-muted">No myopia risk assessments available.</p>
            </div>
            @endif
        </div>
    </div>
    {{-- Myopia-Quiz-Result-Section-End --}}

    {{-- Achievements-Section --}}
    <div class="col-md-6">
        <h2 class="px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Achievements 🏅</h2>
        <div class="row mt-5">
            @if($child->badges->isNotEmpty())
            @foreach($child->badges as $badge)
            <div class="col-md-6 mb-3 d-flex">
                <div class="card shadow-sm border-0 h-100 w-100 d-flex flex-column">
                    <div class="card-body text-center d-flex flex-column">
                        @if($badge->name == 'Vision Hero')
                        <i class="fas fa-eye text-primary fa-3x"></i>
                        @elseif($badge->name == 'Screen Time Ninja')
                        <i class="fas fa-laptop-code text-success fa-3x"></i>
                        @else
                        <i class="fas fa-medal text-warning fa-3x"></i>
                        @endif
                        <h5 class="mt-3">{{ $badge->name }}</h5>
                        <p class="text-muted flex-grow-1">{{ $badge->description }}</p>
                    </div>
                </div>
            </div>
            @endforeach
            @else
            <div class="col-md-12 text-center">
                <p class="text-muted">No achievements earned yet.</p>
            </div>
            @endif
        </div>
    </div>
    {{-- Achievements-Section-End --}}
</div>
{{-- Myopia-Quiz-Result-&-Achievements-Side-by-Side-End --}}

{{-- Child-Document-Section --}}
@if($child->documents->isNotEmpty())
<div class="row p-3">
    <div class="col-md-12">
        <div class="card">
            <h2 class="px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">
                Documents 📚
            </h2>
            <div class="card-body table-responsive">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_customers_table">
                        <thead>
                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                <th class="w-10px pe-2">
                                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                        <input class="form-check-input" type="checkbox" id="selectAll" />
                                    </div>
                                </th>
                                <th class="text-center">Sr. No</th>
                                <th class="text-center">Icon</th>
                                <th>Name</th>
                                <th class="text-center">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="fw-semibold text-gray-600">
                            @foreach($child->documents as $index => $document)
                                <tr>
                                    <td class="text-center">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                                            <input class="form-check-input" type="checkbox" value="{{ $document->id }}" />
                                        </div>
                                    </td>
                                    <td class="text-center">{{ $index + 1 }}</td>
                                    <td class="text-center">
                                        @if ($document->file_type === 'pdf')
                                            <i class="fas fa-file-pdf text-danger fa-2x"></i>
                                        @elseif ($document->file_type === 'image')
                                            <i class="fas fa-file-image text-primary fa-2x"></i>
                                        @elseif ($document->file_type === 'word')
                                            <i class="fas fa-file-word text-info fa-2x"></i>
                                        @else
                                            <i class="fas fa-file-alt text-secondary fa-2x"></i>
                                        @endif
                                    </td>
                                    <td>{{ $document->name }}</td>
                                    <td class="text-center">
                                        {{-- View Action --}}
                                        <a href="{{ $document->file_path }}" title="View" class="me-2" target="_blank">
                                            <i class="fi fi-rr-eye text-primary action-icon"></i>
                                        </a>

                                        {{-- Delete Action --}}
                                        <a href="#" onclick="confirmDelete({{ $document->id }})" title="Delete">
                                            <i class="fi fi-rr-trash text-danger action-icon"></i>
                                        </a>
                                        <form id="delete-form-{{ $document->id }}" action="{{ route('document.destroy', ['user_id' => $document->user_id, 'child_id' => $document->child_id, 'document_id' => $document->id]) }}" method="POST" style="display: none;">
                                            @csrf
                                            @method('DELETE')
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="d-flex justify-content-center">
                        {{-- Add pagination here if applicable --}}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

{{-- Child-Document-Section --}}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function confirmDelete(documentId) {
        // SweetAlert2 confirmation dialog
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you really want to delete this document?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel',
            reverseButtons: true
        }).then((result) => {
            if (result.isConfirmed) {
                document.getElementById('delete-form-' + documentId).submit();
            }
        });
    }
</script>