@extends("admin.layouts.app")

@section('content')
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">User Personal Details</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('userIndex') }}" class="text-muted text-hover-primary">Personal Details</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">View</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                {{-- View-Content --}}
                <div class="card">
                    <div class="card-body p-lg-10">
                        <div class="d-flex flex-column flex-xl-row">
                            <div class="flex-lg-row-fluid me-xl-10 mb-10 mb-xl-0">
                                <div class="mt-n1">
                                    <div class="m-0">
                                        <div class="flex-lg-row-fluid mb-10">
                                            <h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Parent Details</h2>
                                            <div class="mt-5 d-flex align-items-start gap-4">
                                                <!-- User Image -->
                                                <div class="me-3">
                                                    <a href="#">
                                                        <img alt="User Image"
                                                             src="{{ $users->user_image ? asset($users->user_image) : asset('assets/img/eyenstein.png') }}"
                                                             style="height: 150px; width: 150px; object-fit: cover; border: 1px solid #decccc; border-radius: 10px; padding: 14px;" />
                                                    </a>
                                                </div>

                                                <!-- User Info -->
                                                <div class="flex-grow-1">
                                                    <div class="d-flex align-items-center justify-content-between mb-1">
                                                        <div class="d-flex align-items-center gap-3">
                                                            <div class="fw-bold fs-1 text-gray-800">{{ $users->full_name }}</div>
                                                            <span class="badge badge-lg {{ $users->status == 1 ? 'badge-light-success' : 'badge-light-danger' }}">
                                                                {{ $users->status == 1 ? 'Active User' : 'Inactive User' }}
                                                            </span>
                                                        </div>
                                                        <a href="{{ route('userEdit', ['id' => $users->id]) }}" class="btn btn-primary">
                                                            <i class="fas fa-edit text-white"></i> Edit
                                                        </a>
                                                    </div>
                                                    <div class="badge badge-lg badge-light-primary">{{ $users->email }}</div>
                                                    <div class="row g-3 mt-3">
                                                        <div class="col-sm-6">
                                                            <div class="fw-semibold fs-7 text-gray-600 mb-1">
                                                                <i class="fas fa-phone-alt me-2"></i> Phone:
                                                            </div>
                                                            <div class="fw-bold fs-6 text-gray-800">{{ $users->mobile }}</div>
                                                        </div>
                                                        <div class="col-sm-6">
                                                            <div class="fw-semibold fs-7 text-gray-600 mb-1">
                                                                <i class="fas fa-briefcase me-2"></i> Occupation:
                                                            </div>
                                                            <div class="fw-bold fs-6 text-gray-800">{{ $users->occupation }}</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Children Details</h2>
                                        <table class="table align-middle table-row-dashed fs-6" id="kt_customers_table">
                                            <thead>
                                                <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                    <th class="w-10px pe-2">
                                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                            <input class="form-check-input" type="checkbox" id="selectAll" />
                                                        </div>
                                                    </th>
                                                    <th class="w-10px pe-2">Sr.No</th>
                                                    <th class="text-center">Image</th>
                                                    <th>Child Name</th>
                                                    <th>Age</th>
                                                    <th>School</th>
                                                    <th>Existing Eye Power</th>
                                                    <th>Status</th>
                                                    <th class="text-center">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody class="fw-semibold text-gray-600">
                                                @php
                                                    $i = 1;
                                                @endphp
                                                @forelse($users->children as $child)
                                                    <tr id="row-{{ $child->id }}">
                                                        <td class="text-center">
                                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ $child->id }}" />
                                                            </div>
                                                        </td>
                                                        <td class="text-center">{{ $i++ }}</td>
                                                        <td class="text-center">
                                                            @if($child->kid_image)
                                                                <img src="{{ asset($child->kid_image) }}" alt="User Image" width="50" height="50">
                                                            @else
                                                                <img src="{{ asset('assets/img/eyenstein.png') }}" alt="Default Avatar" width="50" height="50">
                                                            @endif
                                                        </td>
                                                        <td>{{ $child->name_of_kid ?? '' }}</td>
                                                        <td>{{ $child->age ?? '' }}</td>
                                                        <td>{{ $child->school ?? '' }}</td>
                                                        <td>{{ $child->existing_eye_power ?? '' }} Eye Power</td>
                                                        <td>
                                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                <input type="checkbox" class="form-check-input status-checkbox" data-id="{{ $child->id }}" data-user-id="{{ $users->id }}" {{ $child->status == 1 ? 'checked' : '' }}>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            <a href="#" class="view-child-details me-2" data-user-id="{{ $users->id }}" data-child-id="{{ $child->id }}" title="View">
                                                                <i class="fi fi-rr-eye text-primary action-icon"></i>
                                                            </a>
                                                            <a href="#" class="view-child-report me-2" data-user-id="{{ $users->id }}" data-child-id="{{ $child->id }}" title="Daliy Quiz Report">
                                                                <i class="fi fi-rr-file-chart-line text-warning action-icon"></i>
                                                            </a>
                                                            <a href="#" class="upload-child-file me-2" data-user-id="{{ $users->id }}" data-child-id="{{ $child->id }}" title="Upload File" onclick="triggerFileUpload({{ $users->id }}, {{ $child->id }})">
                                                                <i class="fi fi-rr-folder-upload text-info action-icon"></i>
                                                            </a>
                                                            <form id="fileUploadForm" style="display: none;" enctype="multipart/form-data">
                                                                @csrf
                                                                <input type="file" id="fileInput" name="file" onchange="uploadFile(event)">
                                                                <input type="hidden" id="userId" name="user_id">
                                                                <input type="hidden" id="childId" name="child_id">
                                                            </form>
                                                            <a href="{{ route('children.edit', ['user_id' => $users->id, 'child_id' => $child->id]) }}" title="Edit" class="me-2">
                                                                <i class="fi fi-rr-edit text-success action-icon"></i>
                                                            </a>
                                                            <a href="#" onclick="event.preventDefault(); confirmDelete({{ $child->id }}, {{ $users->id }})" title="Delete">
                                                                <i class="fi fi-rr-trash text-danger action-icon"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center">No children data available</td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                        <h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;"></h2>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- View-Content --}}

                {{-- Child View Model --}}
                <div class="modal fade" id="childDetailsModal" tabindex="-1" aria-labelledby="childDetailsModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="childDetailsModalLabel">Child Details</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="childDetailsContent">
                                    <p class="text-center">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- Child View Model --}}

                {{-- Child Daily Quiz Report Model --}}
                <div class="modal fade" id="childReportModal" tabindex="-1" aria-labelledby="childReportModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="childReportModalLabel">Daily Quiz Report</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div id="childReportContent">
                                    <p class="text-center">Loading...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- Child Daily Quiz Report Model --}}
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.min.js"></script>
<script>
    function confirmDelete(childId, userId) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to undo this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: `/user/${userId}/children/${childId}`, // Use correct route
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        _method: "DELETE"
                    },
                    success: function(response) {
                        Swal.fire({
                            title: 'Deleted!',
                            text: 'Child deleted successfully.',
                            icon: 'success',
                            timer: 2000,
                            showConfirmButton: false
                        });
                        $(`#row-${childId}`).fadeOut(500, function () { $(this).remove(); });
                    },
                    error: function() {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Something went wrong. Please try again.',
                            icon: 'error'
                        });
                    }
                });
            }
        });
    }
</script>
<script>
    $(document).ready(function() {
        // *********User-Status-Change*********
        $('.status-checkbox').on('change', function() {
            var checkbox = $(this);
            var childId = checkbox.data('id');
            var userId = checkbox.data('user-id');
            var isStatus = checkbox.prop('checked') ? 1 : 0;

            Swal.fire({
                title: 'Are you sure?',
                text: "You are about to " + (isStatus ? "activate" : "deactivate") + " this child.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "/user/" + userId + "/children/" + childId + "/change-status",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            status: isStatus
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire({
                                    title: 'Success!',
                                    text: 'Status updated successfully.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message || 'Something went wrong.',
                                    icon: 'error'
                                });
                                checkbox.prop('checked', !isStatus); // Revert the checkbox state
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'Error!',
                                text: 'An error occurred. Please try again.',
                                icon: 'error'
                            });
                            checkbox.prop('checked', !isStatus); // Revert the checkbox state
                        }
                    });
                } else {
                    checkbox.prop('checked', !isStatus); // Revert the checkbox state if canceled
                }
            });
        });
        // *********Status-Change*********
    });
</script>
<script>
    $(document).ready(function () {
        $('#selectAll').on('change', function () {
            $('.row-checkbox').prop('checked', $(this).prop('checked'));
        });
        // ***********Child Details***********
        $(".view-child-details").on("click", function (e) {
            e.preventDefault();
            
            let userId = $(this).data("user-id");
            let childId = $(this).data("child-id");

            $("#childDetailsContent").html('<p class="text-center">Loading...</p>');
            let modalElement = document.getElementById('childDetailsModal');
            let myModal = new bootstrap.Modal(modalElement);
            myModal.show();

            $.ajax({
                url: `/user/${userId}/children/${childId}`,
                type: "GET",
                success: function (response) {
                    $("#childDetailsContent").html(response);
                },
                error: function () {
                    $("#childDetailsContent").html('<p class="text-center text-danger">Failed to load details.</p>');
                }
            });
        });

        // ***********Report***********
        $(".view-child-report").on("click", function (e) {
            e.preventDefault();

            let userId = $(this).data("user-id");
            let childId = $(this).data("child-id");

            $("#childReportContent").html('<p class="text-center">Loading...</p>');
            // $("#childReportModal").modal("show");
            let modalElement = document.getElementById('childReportModal');
            let myModal = new bootstrap.Modal(modalElement);
            myModal.show();

            $.ajax({
                url: `/user/${userId}/children/daily-quiz-report/${childId}`,
                type: "GET",
                success: function (response) {
                    $("#childReportContent").html(response);
                },
                error: function () {
                    $("#childReportContent").html('<p class="text-center text-danger">Failed to load details.</p>');
                }
            });
        });
    });
</script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    function triggerFileUpload(userId, childId) {
    document.getElementById('userId').value = userId;
    document.getElementById('childId').value = childId;
    document.getElementById('fileInput').click();
    }

    function uploadFile(event) {
        const formData = new FormData(document.getElementById('fileUploadForm'));
        const userId = document.getElementById('userId').value;
        const childId = document.getElementById('childId').value;

        // Show loader before upload begins
        Swal.fire({
            title: 'Uploading...',
            text: 'Please wait while the file is being uploaded.',
            allowOutsideClick: false,
            didOpen: () => {
                Swal.showLoading(); // native loader
            }
        });

        fetch(`/user/${userId}/children/${childId}/upload-file`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('input[name="_token"]').value,
            },
        })
        .then((response) => response.json())
        .then((data) => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: 'Success',
                    text: 'File uploaded successfully!',
                    timer: 2000,
                    showConfirmButton: false
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: 'Error',
                    text: 'File upload failed: ' + data.message,
                });
            }
        })
        .catch((error) => {
            console.error('Error:', error);
            Swal.fire({
                icon: 'error',
                title: 'Error',
                text: 'An error occurred during file upload.',
            });
        });
    }
</script>
@endsection