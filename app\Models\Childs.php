<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Childs extends Model
{
    use HasFactory, SoftDeletes;
    protected $table = 'childrens';
    protected $fillable = [
        'user_id',
        'name_of_kid',
        'kid_image',
        'age',
        'school',
        'existing_eye_power',
        'status',
    ];

    // ************Badges-Relation************
    public function badges()
    {
        return $this->belongsToMany(Badge::class, 'child_badges', 'child_id', 'badge_id');
    }
    // ************Badges-Relation************

    // ************Myopia-Quiz-Assessment-Relation************
    public function myopiaAssessments()
    {
        return $this->hasOne(MyopiaQuizAssessments::class, 'child_id');
    }
    // ************Myopia-Quiz-Assessment-Relation************

    // ************ChildDocuments-Relation************
    public function documents()
    {
        return $this->hasMany(ChildDocuments::class, 'child_id');
    }
    // ************ChildDocuments-Relation************
}
