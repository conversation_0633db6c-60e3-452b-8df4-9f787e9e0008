<?php

use App\Http\Controllers\Api\ApiController;
use App\Http\Controllers\Api\AuthController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::post('/login', [AuthController::class, 'login']);
Route::post('/create-user-profile', [AuthController::class, 'createUserProfile']);

Route::middleware(['jwt.auth'])->group(function () {
    Route::get('/get-user-profile', [ApiController::class, 'getUserProfile']);
    Route::get('/get-child-list', [ApiController::class, 'getChildList']);
    Route::get('/get-child-details/{child_id}', [ApiController::class, 'getChildDetails']);
    Route::post('/update-user-profile', [ApiController::class, 'updateUserProfile']);
    Route::get('/user/{id}/children', [ApiController::class, 'getUserWithChildren']);
    Route::put('/user/{user_id}/child/{child_id}', [ApiController::class, 'updateChildDetails']);
    Route::post('/update-child-intake', [ApiController::class, 'updateisIntake']);

    Route::get('/child_documet/{child_id}', [ApiController::class, 'getDocumentsByChildId']);
    Route::get('/documents/{id}', [ApiController::class, 'getPdfById']);
    Route::post('/add-child-details', [ApiController::class, 'addChildDetails']);
    Route::get('/get-myopia-result', [ApiController::class, 'getMyopiaAssessments']);
    Route::post('/store-myopia-result', [ApiController::class, 'storeMyopiaAssessments']);

    Route::get('/get-today-quiz-result/{child_id}', [ApiController::class, 'getDailyQuizToday']);
    Route::get('/get-daily-quiz-result/{child_id}', [ApiController::class, 'getDailyQuizWeekly']);
    Route::post('/store-daily-quiz-result', [ApiController::class, 'storeDailyQuiz']);

    Route::get('/terms_conditions', [ApiController::class, 'getTnc'])->name('getTnc');
    Route::get('/privacy_policy', [ApiController::class, 'getPrivacyPolicy'])->name('getPrivacyPolicy');

    Route::post('/delete-request', [ApiController::class, 'deleteRequest'])->name('deleteRequest');

    Route::get('/get-doctors', [ApiController::class, 'getDoctors']);
});