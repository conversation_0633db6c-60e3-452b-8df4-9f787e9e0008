<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Badge;
use App\Models\ChildBadge;
use App\Models\ChildDocuments;
use App\Models\Childs;
use App\Models\DailyEyeHealthQuiz;
use App\Models\Doctor;
use App\Models\MyopiaQuizAssessments;
use App\Models\User;
use App\Models\WebSettings;
use App\Services\FirebaseStorageService;
use Carbon\Carbon;
use Dflydev\DotAccessData\Data;
use Exception;
use Google\Protobuf\Internal\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Kreait\Firebase\Factory;

class ApiController extends Controller
{

    protected $firebaseStorageService;

    public function __construct(FirebaseStorageService $firebaseStorageService)
    {
        $this->firebaseStorageService = $firebaseStorageService;
    }


   // **********GET-USER-PROFILE**********
    public function getUserProfile(Request $request)
    {
        try {
            $user_id = Auth::id();
            $user = User::find($user_id);
            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not found'], 404);
            }

            // Return profile data (exclude sensitive info)
            return response()->json([
                'success' => true,
                'user' => [
                    'id' => $user->id,
                    'full_name' => $user->full_name,
                    'mobile' => $user->mobile,
                    'email' => $user->email,
                    'occupation' => $user->occupation,
                    'user_image' => $user->user_image,
                    'created_at' => $user->created_at,
                ]
            ], 200);

            return response()->json(['success' => true, 'message' => 'Get User Profile successfully', 'user' => $user], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error fetching user details'], 500);
        }
    }

    // ****************Child-List****************
    public function getChildList()
    {
        try {
            $userId = Auth::id();
            $allChildren = Childs::with(['myopiaAssessments:id,child_id,risk_level,created_at'])->where('user_id', $userId)->get();
            $transformed = $allChildren->map(function ($child) {
                return [
                    'id' => $child->id,
                    'name_of_kid' => $child->name_of_kid,
                    'age' => $child->age,
                    'school' => $child->school,
                    'risk_level' => optional($child->myopiaAssessments)->risk_level,
                    'assessment_date' => optional($child->myopiaAssessments)?->created_at?->format('d-m-Y'),
                ];
            });
            return response()->json(['success' => true, 'message' => 'Children list fetched', 'data' => $transformed], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error fetching children listing'], 500);
        }
    }
    // ****************Child-List****************

    // **********GET-CHILD-DETAILS**********
    public function getChildDetails($child_id)
    {
        try {
            $child = Childs::with(['myopiaAssessments', 'documents'])->where('id', $child_id)->first();
            if (!$child) {
                return response()->json(['success' => false, 'message' => 'Child not found'], 404);
            }
            if ($child->myopiaAssessments) {
                $assessment = $child->myopiaAssessments;
                $questionnaire = [];
                for ($i = 1; $i <= 10; $i++) {
                    $key = "question_{$i}";
                    $questionnaire[$key] = $assessment->$key;
                    unset($assessment->$key); // Remove from top level
                }
                // Add the questionnaire sub-object
                $assessment->questionnaire = $questionnaire;
            }
            return response()->json(['success' => true, 'message' => 'Child details fetched', 'data' => $child], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error fetching child details'], 500);
        }
    }
    // **********GET-CHILD-DETAILS**********

    public function updateUserProfile(Request $request)
    {
        try {
            $authUser = Auth::user();
            $user = User::find($authUser->id);
            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not authenticated'], 401);
            }
            $validatedData = $request->validate([
                'full_name' => 'required|string|max:255',
                'mobile' => 'nullable|string|max:20',
                'email' => 'required|email|unique:users,email,' . $authUser->id,
                'occupation' => 'nullable|string|max:255',
                // 'user_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $user->full_name = $validatedData['full_name'];
            $user->mobile = $validatedData['mobile'] ?? $user->mobile;
            $user->email = $validatedData['email'];
            $user->occupation = $validatedData['occupation'] ?? $user->occupation;

            // Handle image upload if present
            if ($request->hasFile('user_image')) {
                // Call the FirebaseStorageService to upload the image
                // $imageUrl = $this->firebaseStorageService->uploadImageToFirebase($request->file('user_image'), 'user_images');
                // $user->user_image = $imageUrl;
            }
            $user->save();
            return response()->json(['success' => true, 'message' => 'User profile updated successfully', 'user' => $user], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating profile'], 500);
        }
    }

    public function getUserWithChildren($user_id)
    {
        try {
            // Fetch the user along with their children
            $user = User::with(['children', 'badges'])->find($user_id);
            // dd($user);

            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not found'], 404);
            }

            return response()->json([
                'success' => true,
                'user' => $user,
                'badge' => $user->badges
            ], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error fetching user with children'], 500);
        }
    }

    public function updateChildDetails(Request $request, $user_id, $child_id)
    {
        try {
            // Validate the incoming data
            $validatedData = $request->validate([
                'name_of_kid' => 'required|string|max:255',
                'kid_image' => 'nullable',
                'age' => 'nullable|integer|min:0',
                'school' => 'nullable|string|max:255',
                'existing_eye_power' => 'nullable|string|max:255',
                'status' => 'nullable|boolean',
            ]);

            // Find the user by user_id
            $user = User::find($user_id);
            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not found'], 404);
            }

            // Find the child by child_id
            $child = $user->children()->find($child_id);
            if (!$child) {
                return response()->json(['success' => false, 'message' => 'Child not found'], 404);
            }

            // Update the child's details
            $child->name_of_kid = $validatedData['name_of_kid'];
            $child->kid_image = $validatedData['kid_image'] ?? $child->kid_image;
            $child->age = $validatedData['age'] ?? $child->age;
            $child->school = $validatedData['school'] ?? $child->school;
            $child->existing_eye_power = $validatedData['existing_eye_power'] ?? $child->existing_eye_power;
            $child->status = $validatedData['status'] ?? $child->status;

            // Handle image upload if present
            // if ($request->hasFile('kid_image')) {
            //     $image = $request->file('kid_image');
            //     $imageName = time() . '.' . $image->getClientOriginalExtension();
            //     $image->move(public_path('uploads/kid_images'), $imageName);
            //     $child->kid_image = 'uploads/kid_images/' . $imageName;
            // }

            if ($request->hasFile('kid_image')) {
                $childImageUrl = $this->firebaseStorageService->uploadImageToFirebase($request->file('kid_image'), 'child_images');
                $child->kid_image = $childImageUrl;
            }

            // Save the updated child details
            $child->save();

            return response()->json([
                'success' => true,
                'message' => 'Child details updated successfully',
                'child' => $child
            ], 200);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating child details'], 500);
        }
    }

    // **********GET-CHILD-DOCUMENTS**********
    public function updateisIntake(Request $request)
    {
        try {
            $child = Childs::find($request->child_id);
            if (!$child) {
                return response()->json(['success' => false, 'message' => 'Child not found'], 404);
            }
            $child->isIntakeSubmitted = $request->isIntakeSubmitted;
            $child->save();
            return response()->json(['success' => true, 'message' => 'Child intake updated successfully'], 200);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating child intake'], 500);
        }
    }
    // **********GET-CHILD-DOCUMENTS**********

    //  **********GET-MYOPIA-RISK-ASSESSMENTS-RESULT**********
    public function getMyopiaAssessments()
    {
        try {
            $user = Auth::user();
            $myopiaAssessments = MyopiaQuizAssessments::with(['child:id,name_of_kid'])->where('user_id', $user->id)->select('id', 'child_id', 'total_points', 'risk_level')->get();
            if(!$myopiaAssessments){
                return response()->json(['success' => false, 'message' => 'no Myopia assessments result found'], 404);
            }
            return response()->json(['success' => true, 'message' => 'Myopia assessments result', 'data' => $myopiaAssessments], 200);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }
    //  **********GET-MYOPIA-RISK-ASSESSMENTS-RESULT**********

    //  **********STORE-MYOPIA-RISK-ASSESSMENTS-RESULT**********
    public function storeMyopiaAssessments(Request $request)
    {
        try {
            $user = Auth::user();
            $validatedData = $request->validate([
                'child_id'  => 'required|exists:childrens,id',
                'question_1' => 'required|integer|in:0,2',
                'question_2' => 'required|integer|in:0,2',
                'question_3' => 'required|integer|in:0,2',
                'question_4' => 'required|integer|in:0,2',
                'question_5' => 'required|integer|in:0,2',
                'question_6' => 'required|integer|in:0,2',
                'question_7' => 'required|integer|in:0,2',
                'question_8' => 'required|integer|in:0,2',
                'question_9' => 'required|integer|in:0,2',
                'question_10' => 'required|integer|in:0,2',
            ]);
            $validatedData['user_id'] = $user->id;
            $myopiaAssessments = MyopiaQuizAssessments::create($validatedData);
            $myopiaAssessments->refresh();
            return response()->json(['success' => true, 'message' => 'Myopia assessments stored successfully', 'data' => $myopiaAssessments], 201);
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validation failed', 'errors'  => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }
    //  **********STORE-MYOPIA-RISK-ASSESSMENTS-RESULT**********

    //  **********GET-DAILY-QUIZ-RESULT-TODAY**********
    public function getDailyQuizToday(Request $request)
    {
        try {
            $user = Auth::user();
            $child = $request->child_id;
            $today = Carbon::today();
            $tomorrow = Carbon::tomorrow();

            $quizResult = DailyEyeHealthQuiz::where('user_id', $user->id)->where('child_id', $child)->whereBetween('created_at', [$today, $tomorrow])->first();
            if (!$quizResult) {
                return response()->json(['success' => true, 'message' => 'Quiz not found yet', 'data' => null], 200);
            }
            return response()->json(['success' => true, 'message' => 'Daily result', 'data' => $quizResult], 200);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }
    //  **********GET-DAILY-QUIZ-RESULT-TODAY**********

    //  **********GET-DAILY-QUIZ-RESULT-WEEKLY**********
    public function getDailyQuizWeekly(Request $request)
    {
        try {
            $user = Auth::user();
            $childId = $request->child_id;

            if (!$childId) {
                return response()->json(['success' => false, 'message' => 'Child ID is required'], 422);
            }

            // Define start and end of current week (Mon to Sun)
            $startOfWeek = Carbon::now()->startOfWeek();
            $endOfWeek = Carbon::now()->endOfWeek();

            // Get all daily reports for the current week for user and child
            $quizzes = DailyEyeHealthQuiz::where('user_id', $user->id)->where('child_id', $childId)->whereBetween('created_at', [$startOfWeek, $endOfWeek])->get();

            // Static quotes by day
            $quotes = [
                'Mon' => 'Start your week with 20 minutes of outdoor fun—your eyes will thank you!',
                'Tue' => 'A healthy plate today keeps eye strain away!',
                'Wed' => 'Midweek tip: Blink often and look away from your screen every 20 minutes.',
                'Thu' => 'Strong eyes, strong mind—do your eye exercises today!',
                'Fri' => 'Limit your screen time and enjoy real time with nature.',
                'Sat' => 'Weekends are perfect for bright, natural light—get outside!',
                'Sun' => 'Rest your eyes and recharge. Your vision matters!',
            ];

            // Map results by day abbreviation
            $dayMap = [];
            $totalPoints = 0;
            foreach ($quizzes as $quiz) {
                $carbonDate = Carbon::parse($quiz->created_at);
                $day = $carbonDate->format('D'); // Mon, Tue, etc.

                $dayMap[$day] = [
                    'day' => $day,
                    'date' => $carbonDate->format('d F Y'), // 06 April 2025
                    'quote' => $quotes[$day] ?? '',
                    'user_id' => $quiz->user_id,
                    'child_id' => $quiz->child_id,
                    'outdoor_activity' => $quiz->outdoor_activity,
                    'eye_healthy_food' => $quiz->eye_healthy_food,
                    'eye_exercises' => $quiz->eye_exercises,
                    'screen_time_limit' => $quiz->screen_time_limit,
                    'total_points' => $quiz->total_points,
                    'percentage' => (float) $quiz->percentage,
                ];
                $totalPoints += $quiz->total_points;
            }

            // Days of week from Monday to Sunday
            $weekDays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
            $result = [];
            foreach ($weekDays as $day) {
                if (isset($dayMap[$day])) {
                    $result[] = $dayMap[$day];
                } else {
                    $result[] = [
                        'day' => $day,
                        'date' => Carbon::now()->startOfWeek()->addDays(array_search($day, $weekDays))->format('d F Y'),
                        'quote' => $quotes[$day] ?? '',
                    ];
                }
            }

            $dayCount = count($dayMap); // only days with data

            return response()->json([
                'success' => true,
                'message' => 'Daily quiz result',
                'data' => [
                    'result' => $result,
                    'total_points' => $totalPoints,
                    'day_counts' => $dayCount,
                ]
            ], 200);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }
    //  **********GET-DAILY-QUIZ-RESULT-WEEKLY**********

    //  **********STORE-DAILY-QUIZ-RESULT**********
    public function storeDailyQuiz(Request $request)
    {
        try {
            $user = Auth::user();
            $validatedData = $request->validate([
                'child_id'            => 'required|exists:childrens,id',
                'outdoor_activity'    => 'required|in:0,5',
                'eye_healthy_food'    => 'required|in:0,5',
                'eye_exercises'       => 'required|in:0,5',
                'screen_time_limit'   => 'required|in:0,5',
            ]);
            $validatedData['user_id'] = $user->id;
            $daillyQuizResponse = DailyEyeHealthQuiz::create($validatedData);
            $this->assignBadges($request->child_id);
            $daillyQuizResponse->refresh();
            return response()->json(['success' => true, 'message' => 'Daily quiz stored successfully', 'data' => $daillyQuizResponse], 201);
        } catch (ValidationException $e) {
            return response()->json(['success' => false, 'message' => 'Validation failed', 'errors'  => $e->errors()], 422);
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return response()->json(['success' => false, 'message' => 'An error occurred'], 500);
        }
    }
    //  **********STORE-DAILY-QUIZ-RESULT**********

    // **********CHECK-STREAKS**********
    public function assignBadges($childId)
    {
        // Fetch last 7 days of quiz records (in descending order)
        $streakRecords = DailyEyeHealthQuiz::where('child_id', $childId)->whereDate('created_at', '>=', now()->subDays(7))->orderBy('created_at', 'desc')->pluck('created_at')->toArray();
        if ($this->checkConsecutiveDays($streakRecords, 7)) {  // Check if the child has completed the quiz **consecutively** for 7 days
            $this->giveBadge($childId, 'Vision Hero');
        }

        // Low screen time for a month
        $lowScreenTime = DailyEyeHealthQuiz::where('child_id', $childId)->where('screen_time_limit', 'yes')->whereDate('created_at', '>=', now()->subDays(30))->count();
        if ($lowScreenTime == 30) {
            $this->giveBadge($childId, 'Screen Time Ninja');
        }
    }
    // **********CHECK-STREAKS**********

    // **********ASSIGN-BADGE**********
    private function giveBadge($childId, $badgeName)
    {
        $badge = Badge::where('name', $badgeName)->first();
        if ($badge && !ChildBadge::where('child_id', $childId)->where('badge_id', $badge->id)->exists()) {
            ChildBadge::create([
                'child_id' => $childId,
                'badge_id' => $badge->id,
            ]);
        }
    }
    // **********ASSIGN-BADGE**********

    // **********CHECK-STREAK-CONTINUATION**********
    private function checkConsecutiveDays($dates, $requiredDays)
    {
        if (count($dates) < $requiredDays) {
            return false;
        }
        // Sort dates from oldest to newest
        $dates = array_reverse($dates);
        // Check if each day follows the previous one
        for ($i = 1; $i < count($dates); $i++) {
            $prevDate = Carbon::parse($dates[$i - 1])->startOfDay();
            $currentDate = Carbon::parse($dates[$i])->startOfDay();
            if (!$prevDate->addDay()->equalTo($currentDate)) {
                return false; // Streak is broken
            }
        }
        return true;
    }
    // **********CHECK-STREAK-CONTINUATION**********

    // **********Get-Terms-Conditions**********
    public function getTnc()
    {
        try{
            $tnc = WebSettings::where('type', 'terms_conditions')->first();
            return response()->json(['success' => true, 'message' => 'Terms & conditions', 'data' => $tnc], 200);
        } catch (Exception $e){
            Log::error('Failed to fetch terms & conditions'. ''. $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch terms & conditions'], 500);
        }
    }
    // **********Get-Terms-Conditions**********

    // **********Get-Privacy-Policy**********
    public function getPrivacyPolicy()
    {
        try{
            $privacyPolicy = WebSettings::where('type', 'privacy_policy')->first();
            return response()->json(['success' => true, 'message' => 'Privacy Policy', 'data' => $privacyPolicy], 200);
        } catch (Exception $e){
            Log::error('Failed to fetch privacy policy'. ''. $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to fetch privacy policy'], 500);
        }
    }
    // **********Get-Privacy-Policy**********

    // **********ADD-CHILD-DETAILS**********
    public function addChildDetails(Request $request)
    {
        $request->validate([
            'name_of_kid' => 'required|string',
            'age' => 'required|integer',
            'school' => 'nullable|string',
            'existing_eye_power' => 'nullable|string',
        ]);

        try {
            $user = Auth::user();
            // $childImageUrl = $request->hasFile('kid_image') ? $this->uploadImageToFirebase($request->file('kid_image')) : url('default-img.png');
            $child = Childs::create([
                'user_id' => $user->id,
                'name_of_kid' => $request->name_of_kid,
                // 'kid_image' => $childImageUrl,
                'age' => $request->age,
                'school' => $request->school,
                'existing_eye_power' => $request->existing_eye_power,
            ]);

            return response()->json(['success' => true, 'message' => 'Child details added successfully', 'child' => $child], 200);
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error adding child details'], 500);
        }
    }
    // **********ADD-CHILD-DETAILS**********

    // **********UPLOAD-IMAGE-TO-FIREBASE**********
    private function uploadImageToFirebase($file)
    {
        try {
            $serviceAccountFile = base_path('firebase-credential.json');
            $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');
            $fileName = 'user_image/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );
            return "https://firebasestorage.googleapis.com/v0/b/" . $bucket->name() . "/o/" . urlencode($fileName) . "?alt=media";
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return url('default-img.png');
        }
    }
    // **********UPLOAD-IMAGE-TO-FIREBASE**********

    // **********DELETE-ACCOUNT-REQUEST**********
    public function deleteRequest(Request $request)
    {
        try {
            $request->validate([
                'mobile' => 'required'
            ]);
            $mobile = $request->mobile;
            $user = User::where('mobile', $mobile)->first();
            if (!$user) {
                return response()->json(['success' => false, 'message' => 'User not found with provided mobile number.'], 404);
            }

            // Get data before deletion
            $children = Childs::where('user_id', $user->id)->get();
            $childIds = $children->pluck('id')->toArray();
            $childBadge = ChildBadge::whereIn('child_id', $childIds)->get();
            $dailyEyeHealth = DailyEyeHealthQuiz::where('user_id', $user->id)->get();
            $myopiaRiskResult = MyopiaQuizAssessments::where('user_id', $user->id)->get();

            // Perform soft delete / delete operations
            $user->update(['status' => 0]);
            Childs::whereIn('id', $childIds)->update(['status' => 0]);
            ChildBadge::whereIn('child_id', $childIds)->delete();
            DailyEyeHealthQuiz::where('user_id', $user->id)->delete();
            MyopiaQuizAssessments::where('user_id', $user->id)->delete();

            // Return original data in response
            return response()->json([
                'success' => true,
                'message' => 'User and related data deleted successfully.',
                'data' => [
                    'user' => $user,
                    'children' => $children,
                    'child_badge' => $childBadge,
                    'daily_eye_health' => $dailyEyeHealth,
                    'myopia_risk_result' => $myopiaRiskResult
                ]
            ], 200);

        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return response()->json(['success' => false, 'message' => 'Error sending request to the administrator'], 500);
        }
    }
    // **********DELETE-ACCOUNT-REQUEST**********


    public function getDocumentsByChildId($child_id)
    {
        try {
            $documents = ChildDocuments::where('child_id', $child_id)->get();

            if ($documents->isEmpty()) {
                return response()->json([
                    'message' => 'No documents found for the specified child',
                ], 404);
            }

            return response()->json([
                'message' => 'Documents retrieved successfully.',
                'data' => $documents,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while retrieving documents.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


    public function getPdfById($id)
    {
        try {
            $document = ChildDocuments::find($id);

            if (!$document) {
                return response()->json([
                    'message' => 'No document found.',
                ], 404);
            }

            return response()->json([
                'message' => 'Document retrieved successfully.',
                'data' => $document,
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while retrieving the document.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function getDoctors()
    {
        try {
            $doctors = Doctor::all()->map(function ($doctor) {
                return [
                    "id" => $doctor->id,
                    "name" => $doctor->name,
                    "specialty" => $doctor->specialty,
                    "experience" => $doctor->experience,
                    "imageKey" => $doctor->image_key,
                    "fees" => $doctor->fees ?? "",
                    "ratings" => $doctor->ratings ?? "",
                    "Availability slots" => $doctor->availability_slots ?? "",
                ];
            });

            if ($doctors->isEmpty()) {
                return response()->json([
                    'message' => 'No doctors found.',
                ], 404);
            }

            return response()->json([
                'message' => 'Doctors retrieved successfully.',
                'doctors' => $doctors
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'message' => 'An error occurred while retrieving doctors.',
                'error' => $e->getMessage(),
            ], 500);
        }
    }


}
