<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('myopia_risk_assessments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('child_id')->constrained('childrens')->onDelete('cascade'); // Ensure correct table name

            // Each question stores either 0 or 20 based on the answer
            for ($i = 1; $i <= 10; $i++) {
                $table->tinyInteger("question_$i")->default(0);
            }

            $table->integer('total_points')->default(0);
            $table->string('risk_level', 10)->default('Mild');

            $table->timestamps();
        });

        // Drop triggers if they already exist to avoid conflicts
        DB::unprepared("DROP TRIGGER IF EXISTS calculate_myopia_risk_insert");
        DB::unprepared("DROP TRIGGER IF EXISTS calculate_myopia_risk_update");

        // Create trigger for BEFORE INSERT
        DB::unprepared("
            CREATE TRIGGER calculate_myopia_risk_insert BEFORE INSERT ON myopia_risk_assessments
            FOR EACH ROW
            BEGIN
                SET NEW.total_points =
                    NEW.question_1 + NEW.question_2 + NEW.question_3 + NEW.question_4 + NEW.question_5 +
                    NEW.question_6 + NEW.question_7 + NEW.question_8 + NEW.question_9 + NEW.question_10;

                IF NEW.total_points BETWEEN 0 AND 4 THEN
                    SET NEW.risk_level = 'Mild';
                ELSEIF NEW.total_points BETWEEN 6 AND 10 THEN
                    SET NEW.risk_level = 'Moderate';
                ELSE
                    SET NEW.risk_level = 'Severe';
                END IF;
            END
        ");

        // Create trigger for BEFORE UPDATE
        DB::unprepared("
            CREATE TRIGGER calculate_myopia_risk_update BEFORE UPDATE ON myopia_risk_assessments
            FOR EACH ROW
            BEGIN
                SET NEW.total_points =
                    NEW.question_1 + NEW.question_2 + NEW.question_3 + NEW.question_4 + NEW.question_5 +
                    NEW.question_6 + NEW.question_7 + NEW.question_8 + NEW.question_9 + NEW.question_10;

                IF NEW.total_points BETWEEN 0 AND 4 THEN
                    SET NEW.risk_level = 'Mild';
                ELSEIF NEW.total_points BETWEEN 6 AND 10 THEN
                    SET NEW.risk_level = 'Moderate';
                ELSE
                    SET NEW.risk_level = 'Severe';
                END IF;
            END
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        DB::unprepared("DROP TRIGGER IF EXISTS calculate_myopia_risk_insert");
        DB::unprepared("DROP TRIGGER IF EXISTS calculate_myopia_risk_update");
        Schema::dropIfExists('myopia_risk_assessments');
    }
};
