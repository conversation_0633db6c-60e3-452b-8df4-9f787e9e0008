<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // \App\Models\User::factory(10)->create();

        // \App\Models\User::factory()->create([
        //     'name' => 'Test User',
        //     'email' => '<EMAIL>',
        // ]);
        DB::table('badges')->insert([
            ['name' => 'Vision Hero', 'description' => 'Completed daily quiz for 7 consecutive days', 'required_days' => 7],
            ['name' => 'Screen Time Ninja', 'description' => 'Maintained low screen time for a month', 'required_days' => 30],
        ]);
    }
}
