@extends("admin.layouts.app")

@section('content')
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">User Personal Details</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('userIndex') }}" class="text-muted text-hover-primary">Personal Details</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">View</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                {{-- View-Content --}}
                <div class="card">
                    <div class="card-body p-lg-20">
                        <div class="d-flex flex-column flex-xl-row">
                            <div class="flex-lg-row-fluid me-xl-10 mb-10 mb-xl-0">
                                <div class="mt-n1">
                                    <div class="m-0">
                                        <div class="flex-lg-row-fluid mb-10">
                                            <div class="mt-n1 d-flex">
                                                <div class="me-5">
                                                    <a href="#">
                                                        <img alt="Logo" src="{{ asset('assets/media/avatars/300-1.jpg') }}" style="height: 150px; width: 150px; object-fit: cover;" />
                                                    </a>
                                                </div>
                                                <div class="flex-grow-1">
                                                    <div class="row g-5 mb-5">
                                                        <div class="col-sm-9">
                                                            <div class="fw-bold fs-1 text-gray-800">{{ $children->name_of_kid }}</div>
                                                            <div class="fs-7 text-gray-800">{{ $children->school }}</div>
                                                        </div>
                                                        <div class="col-sm-3">
                                                            <a href="{{ route('children.edit',['user_id' => $children->user_id, 'child_id' => $children->id]) }}" class="btn btn-primary">
                                                                <i class="fas fa-edit text-white"></i> Edit
                                                            </a>
                                                        </div>
                                                    </div>
                                                    <div class="row g-5 mb-5">
                                                        <div class="col-sm-6">
                                                            <div class="fw-semibold fs-7 text-gray-600 mb-1">Status:</div>
                                                            <div class="fw-bold fs-6 text-gray-800 d-flex align-items-center flex-wrap">
                                                                <span class="astrologer-status-badge badge {{ $children->status == 1 ? 'badge-light-success' : 'badge-light-danger' }}">
                                                                    {{ $children->status == 1 ? 'Active' : 'Inactive' }}
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="row g-5 mb-12 mt-5">
                                            <div class="col-sm-6">
                                                <div class="fw-semibold fs-7 text-gray-600 mb-1">
                                                   School Name: <i class="fas fa-phone-alt ms-2"></i>
                                                </div>
                                                <div class="fw-bold fs-6 text-gray-800">{{$children->school}}</div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="fw-semibold fs-7 text-gray-600 mb-1">
                                                    Age: <i class="fas fa-envelope ms-2"></i>
                                                </div>
                                                <div class="fw-bold fs-6 text-gray-800">{{$children->age}}</div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="fw-semibold fs-7 text-gray-600 mb-1">
                                                    Existing Eye Power: <i class="fas fa-envelope ms-2"></i>
                                                </div>
                                                <div class="fw-bold fs-6 text-gray-800">{{$children->existing_eye_power}}</div>
                                            </div>
                                        </div>
                                        
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- View-Content --}}
            </div>
        </div>
    </div>
</div>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {

        // *********Status-Change*********
        $('.status-checkbox').on('change', function() {
            var checkbox = $(this);
            var astrologerId = checkbox.data('id');
            var isStatus = checkbox.prop('checked') ? 1 : 0;
            var badge = checkbox.next('.astrologer-status-badge'); // Target the badge next to the checkbox

            Swal.fire({
                title: 'Are you sure?',
                text: "You are about to " + (isStatus ? "activate" : "deactivate") + " this astrologer.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, confirm it!'
            }).then((result) => {
                if (result.isConfirmed) {
                    $.ajax({
                        url: "{{ route('toggleUserStatus') }}",
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            astrologer_id: astrologerId,
                            status: isStatus
                        },
                        success: function(response) {
                            if (response.success) {
                                // Update the badge text and class based on the new state
                                if (isStatus) {
                                    badge.removeClass('badge-light-danger').addClass('badge-light-success').text('Active');
                                } else {
                                    badge.removeClass('badge-light-success').addClass('badge-light-danger').text('Inactive');
                                }

                                Swal.fire({
                                    title: 'Success!',
                                    text: 'Status updated successfully.',
                                    icon: 'success',
                                    timer: 2000,
                                    showConfirmButton: false
                                });
                            } else {
                                Swal.fire({
                                    title: 'Error!',
                                    text: response.message || 'Something went wrong.',
                                    icon: 'error'
                                });
                                checkbox.prop('checked', !isStatus); // Revert the checkbox state
                            }
                        },
                        error: function() {
                            Swal.fire({
                                title: 'Error!',
                                text: 'An error occurred. Please try again.',
                                icon: 'error'
                            });
                            checkbox.prop('checked', !isStatus); // Revert the checkbox state
                        }
                    });
                } else {
                    checkbox.prop('checked', !isStatus); // Revert the checkbox state if canceled
                }
            });
        });
        // *********Status-Change*********
    });
</script>
@endsection