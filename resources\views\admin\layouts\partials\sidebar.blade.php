<!DOCTYPE html>

<html lang="en">

<!--begin::Body-->

<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true" data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true" data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <!--begin::Theme mode setup on page load-->
    <script>
        var defaultThemeMode = "light";
        var themeMode;
        if (document.documentElement) {
            if (document.documentElement.hasAttribute("data-theme-mode")) {
                themeMode = document.documentElement.getAttribute("data-theme-mode");
            } else {
                if (localStorage.getItem("data-theme") !== null) {
                    themeMode = localStorage.getItem("data-theme");
                } else {
                    themeMode = defaultThemeMode;
                }
            }
            if (themeMode === "system") {
                themeMode = window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
            }
            document.documentElement.setAttribute("data-theme", themeMode);
        }
    </script>
    <!--end::Theme mode setup on page load-->
    <!--begin::sidebar-->
    <div id="kt_app_sidebar" class="app-sidebar flex-column" data-kt-drawer="true" data-kt-drawer-name="app-sidebar" data-kt-drawer-activate="{default: true, lg: false}" data-kt-drawer-overlay="true" data-kt-drawer-width="225px" data-kt-drawer-direction="start"
        data-kt-drawer-toggle="#kt_app_sidebar_mobile_toggle">
        <!--begin::Logo-->
        <div class="px-6 app-sidebar-logo" id="kt_app_sidebar_logo">
            <!--begin::Logo image-->
            <a href="{{route('dashboard')}}">
                <div class="d-flex align-items-center">
                    <img alt="Logo" src="{{asset('assets/img/Eyesight_logo.svg')}}" class="h-25px app-sidebar-logo-default" />
                    <span class="text-white fs-2 font-weight-bold ms-3">EyeFit.AI</span>
                </div>
            </a>
            <!--end::Logo image-->
            <!--begin::Sidebar toggle-->
            <div id="kt_app_sidebar_toggle" class="app-sidebar-toggle btn btn-icon btn-shadow btn-sm btn-color-muted btn-active-color-primary body-bg h-30px w-30px position-absolute top-50 start-100 translate-middle rotate" data-kt-toggle="true" data-kt-toggle-state="active"
                data-kt-toggle-target="body" data-kt-toggle-name="app-sidebar-minimize">
                <!--begin::Svg Icon | path: icons/duotune/arrows/arr079.svg-->
                <span class="rotate-180 svg-icon svg-icon-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.5" d="M14.2657 11.4343L18.45 7.25C18.8642 6.83579 18.8642 6.16421 18.45 5.75C18.0358 5.33579 17.3642 5.33579 16.95 5.75L11.4071 11.2929C11.0166 11.6834 11.0166 12.3166 11.4071 12.7071L16.95 18.25C17.3642 18.6642 18.0358 18.6642 18.45 18.25C18.8642 17.8358 18.8642 17.1642 18.45 16.75L14.2657 12.5657C13.9533 12.2533 13.9533 11.7467 14.2657 11.4343Z" fill="currentColor" />
                        <path d="M8.2657 11.4343L12.45 7.25C12.8642 6.83579 12.8642 6.16421 12.45 5.75C12.0358 5.33579 11.3642 5.33579 10.95 5.75L5.40712 11.2929C5.01659 11.6834 5.01659 12.3166 5.40712 12.7071L10.95 18.25C11.3642 18.6642 12.0358 18.6642 12.45 18.25C12.8642 17.8358 12.8642 17.1642 12.45 16.75L8.2657 12.5657C7.95328 12.2533 7.95328 11.7467 8.2657 11.4343Z" fill="currentColor" />
                    </svg>
                </span>
                <!--end::Svg Icon-->
            </div>
            <!--end::Sidebar toggle-->
        </div>
        <!--end::Logo-->
        <!--begin::sidebar menu-->
        <div class="overflow-hidden app-sidebar-menu flex-column-fluid">
         <div class="sidebar">
            <!--begin::Menu wrapper-->
            <div id="kt_app_sidebar_menu_wrapper" class="my-5 app-sidebar-wrapper" data-kt-scroll="true" data-kt-scroll-activate="true" data-kt-scroll-height="auto" data-kt-scroll-dependencies="#kt_app_sidebar_logo, #kt_app_sidebar_footer"
                data-kt-scroll-wrappers="#kt_app_sidebar_menu" data-kt-scroll-offset="5px" data-kt-scroll-save-state="true">
                <div class="px-3 menu menu-column menu-rounded menu-sub-indention" id="#kt_app_sidebar_menu" data-kt-menu="true" data-kt-menu-expand="false">
                    {{-- Dashboard Link --}}
                    <div class="menu-item menu-accordion">
                        <span class="menu-link {{ (Request::segment(1) == 'dashboard') ? 'active' : ''}}">
                            <span class="menu-icon">
                                <span class="svg-icon svg-icon-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect x="2" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                        <rect opacity="0.3" x="13" y="2" width="9" height="9" rx="2" fill="currentColor" />
                                        <rect opacity="0.3" x="13" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                        <rect opacity="0.3" x="2" y="13" width="9" height="9" rx="2" fill="currentColor" />
                                    </svg>
                                </span>
                            </span>
                            <a class="menu-link" href="{{ route('dashboard') }}">
                                <span class="menu-title">Dashboards</span>
                            </a>
                        </span>
                    </div>
                    {{-- Dashboard Link --}}

                    {{-- Profile Details --}}
                    <div class="pt-5 menu-item">
                        <div class="menu-content">
                            <span class="menu-heading fw-bold text-uppercase fs-7">Profile Details</span>
                        </div>
                    </div>
                    {{-- User Link --}}
                    <div class="menu-item menu-accordion {{ in_array(Request::segment(1), ['user']) ? 'hover show' : '' }}">
                         <a class="menu-link {{ (Request::segment(1) == 'user') ? 'active' : ''}}" href="{{ route('userIndex') }}">
                            <span class="menu-icon">
                                <span class="svg-icon svg-icon-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <!-- Head -->
                                        <circle cx="12" cy="8" r="4" fill="currentColor" />
                                        <!-- Body -->
                                        <path d="M12 14C9.33 14 4 15.34 4 18V20C4 20.55 4.45 21 5 21H19C19.55 21 20 20.55 20 20V18C20 15.34 14.67 14 12 14Z" fill="currentColor" />
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">User Profile</span>
                        </a>
                    </div>
                    {{-- User Link --}}
                    {{-- Profile Details --}}

                    {{-- Settings Module --}}
                    <div class="pt-5 menu-item">
                        <div class="menu-content">
                            <span class="menu-heading fw-bold text-uppercase fs-7"> Settings </span>
                        </div>
                    </div>
                    {{-- Push Notification --}}
                    <div class="menu-item menu-accordion {{ in_array(Request::segment(1), ['send-notification']) ? 'hover show' : '' }}">
                        <a class="menu-link {{ (Request::segment(1) == 'send-notification') ? 'active' : ''}}" href="{{ route('sendNotification') }}">
                            <span class="menu-icon">
                                <span class="svg-icon svg-icon-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <!-- Bell Icon -->
                                        <path d="M12 2C10.3431 2 9 3.34315 9 5V6.06801C6.165 7.152 4 9.828 4 13V17L2 19V20H22V19L20 17V13C20 9.828 17.835 7.152 15 6.06801V5C15 3.34315 13.6569 2 12 2Z" fill="currentColor"/>
                                        <path d="M14 22C14 23.1046 13.1046 24 12 24C10.8954 24 10 23.1046 10 22H14Z" fill="currentColor"/>
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">Send Notification</span>
                        </a>
                    </div>
                    {{-- Push Notification --}}
                    {{-- Web Settings Module Start --}}
                    <div data-kt-menu-trigger="click" class="menu-item menu-accordion {{ in_array(Request::segment(1), ['web-setting']) ? 'hover show' : '' }}">
                        <span class="menu-link">
                            <span class="menu-icon">
                                <span class="svg-icon svg-icon-2">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M19.4 10.6L21.9 9.5C22.2 9.3 22.3 8.9 22.1 8.6L20.2 5.4C20.1 5.2 19.8 5.1 19.6 5.2L17.1 6.3C16.6 5.9 16.1 5.6 15.5 5.3L15.1 2.7C15 2.4 14.7 2.2 14.4 2.2H9.6C9.3 2.2 9 2.4 8.9 2.7L8.5 5.3C7.9 5.6 7.4 5.9 6.9 6.3L4.4 5.2C4.2 5.1 3.9 5.2 3.8 5.4L1.9 8.6C1.7 8.9 1.8 9.3 2.1 9.5L4.6 10.6C4.5 11.1 4.4 11.5 4.4 12C4.4 12.5 4.5 12.9 4.6 13.4L2.1 14.5C1.8 14.7 1.7 15.1 1.9 15.4L3.8 18.6C3.9 18.8 4.2 18.9 4.4 18.8L6.9 17.7C7.4 18.1 7.9 18.4 8.5 18.7L8.9 21.3C9 21.6 9.3 21.8 9.6 21.8H14.4C14.7 21.8 15 21.6 15.1 21.3L15.5 18.7C16.1 18.4 16.6 18.1 17.1 17.7L19.6 18.8C19.8 18.9 20.1 18.8 20.2 18.6L22.1 15.4C22.3 15.1 22.2 14.7 21.9 14.5L19.4 13.4C19.5 12.9 19.6 12.5 19.6 12C19.6 11.5 19.5 11.1 19.4 10.6ZM12 14.5C10.6 14.5 9.5 13.4 9.5 12C9.5 10.6 10.6 9.5 12 9.5C13.4 9.5 14.5 10.6 14.5 12C14.5 13.4 13.4 14.5 12 14.5Z" fill="currentColor"/>
                                    </svg>
                                </span>
                            </span>
                            <span class="menu-title">Web Settings</span>
                            <span class="menu-arrow"></span>
                        </span>
                        <div class="menu-sub menu-sub-accordion">
                            <div class="menu-item">
                                <a class="menu-link {{ (Request::segment(2) == 'privacy-policy') ? 'active' : ''}} " href="{{ route('privacyPolicy') }}">
                                    <span class="menu-bullet">
                                        <span class="bullet bullet-dot"></span>
                                    </span>
                                    <span class="menu-title">Privacy Policy</span>
                                </a>
                            </div>
                            <div class="menu-item">
                                <a class="menu-link {{ (Request::segment(2) == 'tnc') ? 'active' : ''}} " href="{{ route('termsNcondition') }}">
                                    <span class="menu-bullet">
                                        <span class="bullet bullet-dot"></span>
                                    </span>
                                    <span class="menu-title">Terms & Conditions</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    {{-- Web Settings Module Start --}}
                </div>
            </div>
        </div>
    </div>
    </div>

</body>

</html>