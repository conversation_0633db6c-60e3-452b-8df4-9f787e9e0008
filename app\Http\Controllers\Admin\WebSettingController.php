<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PushNotification;
use App\Models\User;
use App\Models\WebSettings;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Messaging\CloudMessage;

class WebSettingController extends Controller
{
    // ******************Terms-And-Conditions******************
    public function termsNcondition()
    {
        try{
            $tnc = WebSettings::where('type', 'terms_conditions')->first();
            return view('admin.web_settings.tnc', compact('tnc'));
        }catch (Exception $e){
            Log::error('Failed to load terms and conditions:'.''.$e->getMessage());
            return redirect()->back()->with('error', 'Failed to load terms and conditions');
        }
    }
    // ******************Terms-And-Conditions******************

    // ******************Privacy-And-Policy******************
    public function privacyPolicy()
    {
        try{
            $privacyPolicy = WebSettings::where('type', 'privacy_policy')->first();
            return view('admin.web_settings.privacyPolicy', compact('privacyPolicy'));
        }catch (Exception $e){
            Log::error('Failed to load terms and conditions:'.''.$e->getMessage());
            return redirect()->back()->with('error', 'Failed to load terms and conditions');
        }
    }
    // ******************Privacy-And-Policy******************

    // ******************Update-Web-Settings******************
    public function updateWebSettings(Request $request, $id)
    {
        $request->validate([
            'values' => 'required|string',
            'type' => 'required|string'
        ]);

        try {
            $tnc = WebSettings::findOrFail($id);
            $tnc->type = $request->type;
            $tnc->value = $request->values;
            $tnc->save();
            return response()->json(['success' => true]);
        } catch (\Exception $e) {
            Log::error('Failed To update web setting: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }
    // ******************Update-Web-Settings******************

    // ******************Push-Notification******************
    public function pushNotificationShow(Request $request)
    {
        try {
            $notifications = PushNotification::simplePaginate(10);

            $query = User::query();
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where('full_name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%")
                    ->orWhere('mobile', 'LIKE', "%{$search}%")
                    ->orWhere('occupation', 'LIKE', "%{$search}%")
                    ->orWhere('created_at', 'LIKE', "%{$search}%");
            }
            $users = $query->where('status', 1)->orderBy('full_name', 'asc')->simplePaginate(5);
            return view('admin.web_settings.pushNotification', compact('users', 'notifications'));
        } catch (\Exception $e) {
            Log::error('Failed To send push notification: ' . $e->getMessage());
            return response()->json(['success' => false], 500);
        }
    }

    public function pushNotificationStore(Request $request)
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'img_url' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5048',
            'users' => 'required|in:all,selected_only',
            'selected_users' => 'required_if:users,selected_only',
        ], [
            'selected_users.required_if' => 'Please select at least one user from the list.'
        ]);

        // Handle file upload
        $imagePath = null;
        if ($request->hasFile('img_url')) {
            $imagePath = $this->uploadImageToFirebase($request->file('img_url'));
        }

        PushNotification::create([
            'title' => $validated['title'],
            'message' => $validated['message'],
            'img_url' => $imagePath,
            'users' => $validated['users'],
            'selected_users' => $request->selected_users ?? null,
        ]);

        // Fetch FCM tokens
        $query = User::query();
        if ($validated['users'] === 'selected_only') {
            $selectedUsers = $request->selected_users;
            // Convert comma-separated string to array if necessary
            if (is_string($selectedUsers)) {
                $selectedUsers = explode(',', $selectedUsers);
            }
            $query->whereIn('id', $selectedUsers);
        }
        $tokens = $query->whereNotNull('fcm_id')->pluck('fcm_id')->toArray();
        if (!empty($tokens)) {
            $this->sendNotification($tokens, $validated['title'], $validated['message'], $imagePath);
        }
        return response()->json(['message' => 'Notification sent successfully!']);
    }

    private function uploadImageToFirebase($file)
    {
        try {
            $serviceAccountFile = base_path('firebase-credential.json');
            $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');
            $fileName = 'notification_image/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );
            return "https://firebasestorage.googleapis.com/v0/b/" . $bucket->name() . "/o/" . urlencode($fileName) . "?alt=media";
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return url('default-img.png');
        }
    }

    private function sendNotification(array $tokens, $title, $body, $image = null)
    {
        $serviceAccountFile = base_path('firebase-credential.json');
        $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
        $messaging = $firebase->createMessaging();
        $notification = Notification::create($title, $body);
        if ($image) {
            $notification = $notification->withImage($image);
        }
        foreach ($tokens as $token) {
            $message = CloudMessage::new()->withNotification($notification);
            try {
                $messaging->send($message->withNotification($notification)->withData([]), $token);
            } catch (\Throwable $e) {
                Log::error("Failed to send FCM notification: " . $e->getMessage());
            }
        }
    }

    public function pushNotificationDestroy($id)
    {
        try {
            $notification = PushNotification::findOrFail($id);
            $notification->delete();
            return response()->json(['success' => true, 'message' => 'Notification deleted successfully.']);
        } catch (\Exception $e) {
            Log::error('Failed To delete notification: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Failed to delete notification.'], 500);
        }
    }

    public function pushNotificationBulkDelete(Request $request)
    {
        try {
            $ids = $request->input('ids');
            if (!$ids || !is_array($ids)) {
                return response()->json(['success' => false, 'message' => 'No notifications selected.']);
            }
            PushNotification::whereIn('id', $ids)->delete();
            return response()->json(['success' => true, 'message' => 'Selected notifications deleted successfully.']);
        } catch (\Exception $e) {
            Log::error('Bulk delete failed: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Bulk delete failed.'], 500);
        }
    }
    // ******************Push-Notification******************
}
