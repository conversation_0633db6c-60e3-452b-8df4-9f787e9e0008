<?php
namespace App\Services;

use Exception;
use Firebase\JWT\JWT;
use Kreait\Firebase\Factory;
use Illuminate\Support\Facades\Log;

class FirebaseStorageService
{
    public function uploadImageToFirebase($file, $folderName = 'default')
    {
        try {
            $serviceAccountFile = base_path('firebase-credential.json');
            $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');
            // Generate a unique file name
            $fileName = $folderName . '/' . uniqid() . '.' . $file->getClientOriginalExtension();
            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );
            return "https://firebasestorage.googleapis.com/v0/b/" . $bucket->name() . "/o/" . urlencode($fileName) . "?alt=media";
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return url('default-img.png');
        }
    }
}
