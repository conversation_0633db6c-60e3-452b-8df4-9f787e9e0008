<?php

use Firebase\JWT\JWT;
use Illuminate\Support\Facades\Log;
use Kreait\Firebase\Factory;
use Kreait\Firebase\Exception\Auth\UserNotFound;

if (!function_exists('uploadImageToFirebase')) {
    function uploadImageToFirebase($file, $folder = 'user_image')
    {
        try {
            $serviceAccountFile = base_path('firebase_credential.json');
            $firebase = (new Factory)->withServiceAccount($serviceAccountFile);
            $storage = $firebase->createStorage();
            $bucket = $storage->getBucket('eyefit-217d9.firebasestorage.app');

            $fileName = $folder . '/' . uniqid() . '.' . $file->getClientOriginalExtension();

            $bucket->upload(
                fopen($file->getRealPath(), 'r'),
                ['name' => $fileName]
            );

            return "https://firebasestorage.googleapis.com/v0/b/bucket_name/o/" . urlencode($fileName) . "?alt=media";
        } catch (Exception $e) {
            Log::error(['message' => $e->getMessage()]);
            return url('default-img.png');
        }
    }
}

if (!function_exists('verify_firebase_user')) {
    function verify_firebase_user($firebase_token)
    {
        $firebaseConfigPath = base_path('firebase-credential.json');
        if (file_exists($firebaseConfigPath)) {
            $factory = (new Factory)->withServiceAccount($firebaseConfigPath);
            $firebaseAuth = $factory->createAuth();

            try {
                $decodedToken = $firebaseAuth->verifyIdToken($firebase_token);
                return $decodedToken->claims()->get('sub') ? true : false;
            } catch (UserNotFound $e) {
                Log::error($e->getMessage());
                return false;
            }
        }
        return false;
    }
}

if (!function_exists('generate_jwt_token')) {
    function generate_jwt_token($user_id, $firebase_token)
    {
        $key = env('JWT_SECRET_KEY');
        $payload = [
            'iat' => time(),
            'exp' => time() + (30 * 60 * 60 * 24), // 30 days
            'user_id' => $user_id,
            'firebase_id' => $firebase_token,
            'sub' => 'Punjabi Samaj',
        ];
        return JWT::encode($payload, $key, 'HS256');
    }
}
