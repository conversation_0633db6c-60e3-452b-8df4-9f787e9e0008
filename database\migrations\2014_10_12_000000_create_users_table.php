<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('full_name')->nullable();
            $table->string('mobile');
            $table->string('email')->unique()->nullable();
            $table->string('occupation')->nullable();
            $table->text('user_image')->nullable();
            $table->string('password')->nullable();
            $table->text('firebase_id')->nullable();
            $table->text('fcm_id')->nullable();
            $table->text('api_token')->nullable();
            $table->tinyInteger('status')->default(1)->comment('1=>active, 0=>inactive');
            $table->string('login_type')->default(1)->comment('1=>mobile_number, 2=>o-auth');
            $table->rememberToken()->nullable();
            $table->timestamp('email_verified_at')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
