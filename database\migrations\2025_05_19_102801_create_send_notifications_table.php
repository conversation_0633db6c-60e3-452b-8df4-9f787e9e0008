<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('send_notifications', function (Blueprint $table) {
            $table->id();
            $table->text('selected_users')->nullable();
            $table->string('users')->nullable();
            $table->string('title')->nullable();
            $table->text('message')->nullable();
            $table->text('img_url')->nullable();
            $table->tinyInteger('status')->default(1)->comment('1=>sent, 2=>read');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('send_notifications');
    }
};
