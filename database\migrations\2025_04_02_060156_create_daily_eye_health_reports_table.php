<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_eye_health_reports', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('child_id');

            // Now storing integer scores instead of 'yes/no'
            $table->integer('outdoor_activity');
            $table->integer('eye_healthy_food');
            $table->integer('eye_exercises');
            $table->integer('screen_time_limit');

            $table->integer('total_points')->nullable();
            $table->decimal('percentage', 5, 2)->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('child_id')->references('id')->on('childrens')->onDelete('cascade');
        });

        // Drop old triggers if they exist (optional but recommended)
        DB::unprepared("DROP TRIGGER IF EXISTS before_eye_health_insert");
        DB::unprepared("DROP TRIGGER IF EXISTS before_eye_health_update");

        // Recreate triggers with new logic
        DB::unprepared("
            CREATE TRIGGER before_eye_health_insert
            BEFORE INSERT ON daily_eye_health_reports
            FOR EACH ROW
            BEGIN
                DECLARE total INT DEFAULT 0;
                SET total =
                    IFNULL(NEW.outdoor_activity, 0) +
                    IFNULL(NEW.eye_healthy_food, 0) +
                    IFNULL(NEW.eye_exercises, 0) +
                    IFNULL(NEW.screen_time_limit, 0);

                SET NEW.total_points = total;
                SET NEW.percentage = (total / 20) * 100;
            END;
        ");

        DB::unprepared("
            CREATE TRIGGER before_eye_health_update
            BEFORE UPDATE ON daily_eye_health_reports
            FOR EACH ROW
            BEGIN
                DECLARE total INT DEFAULT 0;
                SET total =
                    IFNULL(NEW.outdoor_activity, 0) +
                    IFNULL(NEW.eye_healthy_food, 0) +
                    IFNULL(NEW.eye_exercises, 0) +
                    IFNULL(NEW.screen_time_limit, 0);

                SET NEW.total_points = total;
                SET NEW.percentage = (total / 20) * 100;
            END;
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_eye_health_reports');
        DB::unprepared("DROP TRIGGER IF EXISTS before_eye_health_insert");
        DB::unprepared("DROP TRIGGER IF EXISTS before_eye_health_update");
    }
};
