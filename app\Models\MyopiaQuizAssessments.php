<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MyopiaQuizAssessments extends Model
{
    use HasFactory;
    protected $table = 'myopia_risk_assessments';
    protected $fillable = [
        'user_id',
        'child_id',
        'question_1',
        'question_2',
        'question_3',
        'question_4',
        'question_5',
        'question_6',
        'question_7',
        'question_8',
        'question_9',
        'question_10',
        'total_points',
        'risk_level',
    ];
    /**
     * Get the child that owns the quiz.
     */
    public function child()
    {
        return $this->belongsTo(Childs::class);
    }

    /**
     * Get the user who owns the quiz through the child.
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
