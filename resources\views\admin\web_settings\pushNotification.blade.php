@extends("admin.layouts.app")

@section('content')
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Send Notification</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('userIndex') }}" class="text-muted text-hover-primary">Web Settings</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">Send Notification</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                {{-- View-Content --}}
                <div class="card">
                    <div class="card-body p-lg-10">
                        <div class="d-flex flex-column flex-xl-row">
                            <div class="flex-lg-row-fluid me-xl-10 mb-10 mb-xl-0">
                                <div class="mt-n1">
                                    <div class="m-0">
                                        <div class="row">
                                            {{-- **********Send-Notification-Form********** --}}
                                            <div class="col-md-6">
                                                <h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Send Notification</h2>
                                                <form id="sendNotificationForm" method="POST" enctype="multipart/form-data">
                                                    @csrf
                                                    <div class="card">
                                                        <div class="card-body">
                                                            <div class="row">
                                                                <div class="col-sm-12 mb-6">
                                                                    <input type="hidden" name="selected_users" id="selected_users">
                                                                    <label for="title" class="form-label">Select Users<span class="text-danger">*</span></label>
                                                                    <select name="users" id="users" class="form-control form-control-solid">
                                                                        {{-- <option value="" selected disabled>Select User Type</option> --}}
                                                                        <option value="selected_only">Selected Only</option>
                                                                        <option value="all">All</option>
                                                                    </select>
                                                                    <div class="text-danger mt-2 error-message" data-field="selected_users"></div>
                                                                </div>
                                                                <div class="col-sm-12 mb-3">
                                                                    <label for="title" class="form-label">Title<span class="text-danger">*</span></label>
                                                                    <input type="text" class="form-control form-control-solid" id="title" name="title" placeholder="Enter title">
                                                                    <div class="text-danger mt-2 error-message" data-field="title"></div>
                                                                </div>
                                                                <div class="col-sm-12 mb-3">
                                                                    <label for="message" class="form-label">Message<span class="text-danger">*</span></label>
                                                                    <textarea class="form-control form-control-solid" id="message" name="message" rows="5" placeholder="Enter message"></textarea>
                                                                    <div class="text-danger mt-2 error-message" data-field="message"></div>
                                                                </div>
                                                                <div class="col-sm-12 mb-3">
                                                                    <label for="img_url" class="form-label">Image</label>
                                                                    <input type="file" class="form-control form-control-solid" id="img_url" name="img_url" placeholder="Enter image">
                                                                    <div class="text-danger mt-2 error-message" data-field="img_url"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="ms-10">
                                                        <button type="submit" id="submitForm" class="btn btn-primary btn-sm">Send</button>
                                                    </div>
                                                </form>
                                            </div>
                                            {{-- **********Send-Notification-Form********** --}}
                                            {{-- **********Users-List********** --}}
                                            <div class="col-md-6">
                                                <h2 class="px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Users List</h2>
                                                <input type="search" class="form-control search w-100 mt-5 mb-5" name="search" id="search" placeholder="Search here...">
                                                <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_customers_table">
                                                    <thead>
                                                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                            <th class="w-10px pe-2">
                                                                <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                                    <input class="form-check-input" type="checkbox" id="selectAll" />
                                                                </div>
                                                            </th>
                                                            <th class="w-10px pe-2">Sr.No</th>
                                                            <th class="text-center">Image</th>
                                                            <th>Parent Name</th>
                                                            <th>Mobile</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="user-table-body" class="fw-semibold text-gray-600">
                                                        @if ($users->count() > 0)
                                                            @php
                                                                $i = 1;
                                                            @endphp
                                                            @foreach($users as $item)
                                                                @if($item->is_admin != 1)
                                                                <tr>
                                                                    <td class="text-center">
                                                                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                            <input class="form-check-input row-checkbox" type="checkbox" value="{{ $item->id }}" />
                                                                        </div>
                                                                    </td>
                                                                    <td class="text-center">{{ $i }}</td>
                                                                    <td class="text-center">
                                                                        @if($item->user_image)
                                                                            <img src="{{ asset($item->user_image) }}" alt="User Image" width="50" height="50">
                                                                        @else
                                                                            <img src="{{ asset('assets/img/eyenstein.png') }}" alt="Default Avatar" width="50" height="50">
                                                                        @endif
                                                                    </td>
                                                                    <td>{{ $item->full_name ?? '' }}</td>
                                                                    <td>{{ $item->mobile ?? '' }}</td>
                                                                </tr>
                                                                @endif
                                                                @php $i++; @endphp
                                                            @endforeach
                                                        @else
                                                            <tr>
                                                                <td colspan="8" class="text-center">No specific record found for this query.</td>
                                                            </tr>
                                                        @endif
                                                    </tbody>
                                                </table>
                                                <div class="d-flex justify-content-center" id="pagination-links">
                                                    {!! $users->withQueryString()->links() !!}
                                                </div>
                                            </div>
                                            {{-- **********Users-List********** --}}
                                        </div>
                                        {{-- **********Send-Notification-Data********** --}}
                                        <div class="row mt-8">
                                            <div class="col-md-12">
                                                <h2 class="mb-5 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Send Notification Data</h2>
                                                <div class="d-flex justify-content-end d-none" id="delete-all">
                                                    <button type="button" class="btn btn-danger btn-sm d-flex align-items-center gap-2" onclick="confirmDeleteAll()">
                                                        <i class="fi fi-rr-trash text-white action-icon"></i>Delete All
                                                    </button>
                                                </div>
                                                <table class="table align-middle table-row-dashed fs-6 gy-5" id="notification_data_table">
                                                    <thead>
                                                        <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                            <th class="w-10px pe-2">
                                                                <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                                    <input class="form-check-input" type="checkbox" id="selectPushAll" />
                                                                </div>
                                                            </th>
                                                            <th class="w-10px pe-2">Sr.No</th>
                                                            <th class="text-center">Image</th>
                                                            <th>Title</th>
                                                            <th>Message</th>
                                                            <th>Created At</th>
                                                            <th>Status</th>
                                                            <th class="text-center">Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody class="fw-semibold text-gray-600">
                                                        @if ($notifications->count() > 0)
                                                        @foreach($notifications as $item)
                                                            <tr>
                                                                <td class="text-center">
                                                                    <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                        <input class="form-check-input push-row-checkbox" type="checkbox" value="{{ $item->id }}" />
                                                                    </div>
                                                                </td>
                                                                <td class="text-center">{{ $loop->iteration }}</td>
                                                                <td class="text-center">
                                                                    @if($item->img_url)
                                                                        <img src="{{ $item->img_url }}" alt="Avatar" style="width: 40px; height: 40px; object-fit: cover; border-radius: 50%;">
                                                                    @else
                                                                        <span>—</span>
                                                                    @endif
                                                                </td>
                                                                <td>{{ $item->title ?? '' }}</td>
                                                                <td>{{ $item->message ?? '' }}</td>
                                                                <td>{{ $item->created_at ? \Carbon\Carbon::parse($item->created_at)->format('Y-m-d') : '' }}</td>
                                                                <td>
                                                                    <span class="badge badge-light-{{ $item->status == 1 ? 'success' : 'danger' }}">
                                                                        {{ $item->status == 1 ? 'Sent' : 'Read' }}
                                                                    </span>
                                                                </td>
                                                                <td class="text-center">
                                                                    <a href="javascript:void(0)" title="Delete" onclick="confirmDelete({{ $item->id }})">
                                                                        <i class="fi fi-rr-trash text-danger action-icon"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                        @else
                                                            <tr>
                                                                <td colspan="8" class="text-center">No specific record found for this query.</td>
                                                            </tr>
                                                        @endif
                                                    </tbody>
                                                </table>
                                            </div>
                                            <div class="d-flex justify-content-center">
                                                {{ $notifications->links() }}
                                            </div>
                                        </div>
                                        {{-- **********Send-Notification-Data********** --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {{-- View-Content --}}
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function () {
        let isSelectAll = false; // ✅ Global flag to track selectAll
        let selectedUserIds = new Set(); // ✅ Optional: Store selected user IDs if needed

        // Function to check if all checkboxes are selected
        function areAllChecked() {
            return $('.row-checkbox').length === $('.row-checkbox:checked').length;
        }

        // Function to handle checkbox state changes
        function handleCheckboxStateChange() {
            if (areAllChecked()) {
                $('#selectAll').prop('checked', true);
                $('#users').val('all').trigger('change');
                $('.row-checkbox').prop('disabled', true);
                isSelectAll = true;
            } else if ($('.row-checkbox:checked').length > 0) {
                $('#selectAll').prop('checked', false);
                $('#users').val('selected_only').trigger('change');
                $('.row-checkbox').prop('disabled', false);
                isSelectAll = false;
            } else {
                $('#selectAll').prop('checked', false);
                $('#users').val('').trigger('change');
                $('.row-checkbox').prop('disabled', false);
                isSelectAll = false;
            }
            // Update selectedUserIds if needed
            $('.row-checkbox').each(function () {
                const userId = $(this).val();
                if ($(this).is(':checked')) {
                    selectedUserIds.add(userId);
                } else {
                    selectedUserIds.delete(userId);
                }
            });
        }

        // "Select All" checkbox logic
        $('#selectAll').on('change', function () {
            const isChecked = $(this).prop('checked');
            isSelectAll = isChecked;
            $('.row-checkbox').prop('checked', isChecked);

            if (isChecked) {
                $('#users').val('all').trigger('change');
                $('.row-checkbox').prop('disabled', true);
                // Add all current checkbox IDs to selected list
                $('.row-checkbox').each(function () {
                    selectedUserIds.add($(this).val());
                });
            } else {
                $('#users').val('selected_only').trigger('change');
                $('.row-checkbox').prop('disabled', false);
                // Remove only current page's checkboxes
                $('.row-checkbox').each(function () {
                    selectedUserIds.delete($(this).val());
                });
            }
        });

        // Individual checkbox logic
        $(document).on('change', '.row-checkbox', function () {
            handleCheckboxStateChange();
        });

        // Dropdown change logic
        $('#users').on('change', function () {
            const value = $(this).val();
            if (value === 'all') {
                $('#selectAll').prop('checked', true);
                $('.row-checkbox').prop('checked', true).prop('disabled', true);
            } else if (value === 'selected_only') {
                $('#selectAll').prop('checked', false);
                $('.row-checkbox').prop('disabled', false);
            } else {
                $('#selectAll').prop('checked', false);
                $('.row-checkbox').prop('checked', false).prop('disabled', false);
            }
        });

        // Reapply checkbox state after AJAX pagination or search
        function reapplyCheckboxState() {
            if (isSelectAll) {
                $('.row-checkbox').each(function () {
                    $(this).prop('checked', true).prop('disabled', true);
                });
                $('#selectAll').prop('checked', true);
                $('#users').val('all').trigger('change');
            } else {
                $('.row-checkbox').each(function () {
                    const id = $(this).val();
                    if (selectedUserIds.has(id)) {
                        $(this).prop('checked', true);
                    } else {
                        $(this).prop('checked', false);
                    }
                    $(this).prop('disabled', false);
                });

                $('#selectAll').prop('checked', areAllChecked());
            }
        }

        // Fetch table data (for search)
        function fetchData(searchQuery = '') {
            $.ajax({
                url: "{{ route('sendNotification') }}",
                type: "GET",
                data: { search: searchQuery },
                success: function (data) {
                    $('tbody').html($(data).find('tbody').html());

                    reapplyCheckboxState(); // ✅ Reapply after search
                }
            });
        }
        // Search box event
        $('#search').on('input', function () {
            let searchQuery = $(this).val();
            fetchData(searchQuery);
        });
        $('#search').on('search', function () {
            if ($(this).val() === '') {
                fetchData();
            }
        });

        // AJAX Pagination
        $(document).on('click', '#pagination-links a', function (e) {
            e.preventDefault();
            var url = $(this).attr('href');

            $.ajax({
                url: url,
                type: 'GET',
                success: function (response) {
                    $('#user-table-body').html($(response).find('#user-table-body').html());
                    $('#pagination-links').html($(response).find('#pagination-links').html());

                    reapplyCheckboxState(); // ✅ Reapply after pagination
                },
                error: function (xhr) {
                    console.error('Pagination error:', xhr);
                }
            });
        });
    });
</script>
{{-- Form-Script --}}
<script>
    $(document).ready(function () {
        $('#sendNotificationForm').on('submit', function (e) {
            e.preventDefault();

            // Clear previous error messages
            $('.error-message').text('');

            let form = $(this)[0];
            let formData = new FormData(form);

            // Collect selected user IDs if "selected_only" is chosen
            if ($('#users').val() === 'selected_only') {
                let selectedUserIds = [];
                $('.row-checkbox:checked').each(function () {
                    selectedUserIds.push($(this).val());
                });
                formData.append('selected_users', selectedUserIds.join(','));
            }

            $.ajax({
                url: "{{ route('sendNotification.store') }}", // Adjust to your actual route
                type: "POST",
                data: formData,
                processData: false,
                contentType: false,
                beforeSend: function () {
                    $('#submitForm').prop('disabled', true).text('Sending...');
                },
                success: function (response) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Notification Sent',
                        text: response.message || 'The notification was sent successfully.',
                    });

                    $('#sendNotificationForm')[0].reset();
                    $('#users').val('').trigger('change');
                    $('.row-checkbox').prop('checked', false).prop('disabled', false);
                    $('#selectAll').prop('checked', false);
                    location.reload();
                },
                error: function (xhr) {
                    $('#submitForm').prop('disabled', false).text('Send');
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        $.each(errors, function (key, value) {
                            $('.error-message[data-field="' + key + '"]').text(value[0]);
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Error',
                            text: 'Something went wrong. Please try again.',
                        });
                    }
                },
                complete: function () {
                    $('#submitForm').prop('disabled', false).text('Send');
                }
            });
        });
    });
</script>
{{-- Form-Script --}}
<script>
    function confirmDelete(id) {
        Swal.fire({
            title: "Are you sure?",
            text: "You won't be able to revert this!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Yes, delete it!"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: '/send-notification/delete/' + id,
                    type: 'DELETE',
                    data: {
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Reload only the table body
                            $('#notification_data_table').load(location.href + ' #notification_data_table > *');
                            Swal.fire("Deleted!", "Notification has been deleted.", "success");
                        } else {
                            Swal.fire("Error!", "Something went wrong.", "error");
                        }
                    },
                    error: function(xhr) {
                        Swal.fire("Error!", "Server error occurred.", "error");
                    }
                });
            }
        });
    }
</script>
<script>
    // Toggle visibility of "Delete All" button
    $(document).on('change', '.push-row-checkbox, #selectPushAll', function () {
        let checkedCount = $('.push-row-checkbox:checked').length;
        $('#delete-all').toggleClass('d-none', checkedCount === 0);
    });

    // Select all checkbox logic
    $('#selectPushAll').on('change', function () {
        $('.push-row-checkbox').prop('checked', this.checked).trigger('change');
    });

    // Confirm delete all
    function confirmDeleteAll() {
        let selectedIds = [];
        $('.push-row-checkbox:checked').each(function () {
            selectedIds.push($(this).val());
        });

        if (selectedIds.length === 0) {
            Swal.fire("No selection", "Please select at least one notification.", "info");
            return;
        }

        Swal.fire({
            title: "Are you sure?",
            text: "All selected notifications will be deleted!",
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#d33",
            cancelButtonColor: "#3085d6",
            confirmButtonText: "Yes, delete all!"
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
                    url: "{{ route('sendNotification.bulkDelete') }}",
                    type: "POST",
                    data: {
                        _token: '{{ csrf_token() }}',
                        ids: selectedIds
                    },
                    success: function (response) {
                        if (response.success) {
                            $('#notification_data_table').load(location.href + ' #notification_data_table > *', function () {
                                $('#delete-all').addClass('d-none');
                            });
                            Swal.fire("Deleted!", "Selected notifications have been deleted.", "success");
                        } else {
                            Swal.fire("Error!", "Something went wrong.", "error");
                        }
                    },
                    error: function (xhr) {
                        Swal.fire("Error!", "Server error occurred.", "error");
                    }
                });
            }
        });
    }
</script>
@endsection