@if($dailyQuizReport->isNotEmpty())
<h2 class="mb-4 px-4 py-2 text-black" style="background: rgba(0, 0, 0, 0.1); border-radius: 5px;">Daily Quiz Report 📊</h2>

@php
    $questions = [
        'Did you spend at least 1 hour outdoors today?' => 'outdoor_activity',
        'Did you eat at least one eye healthy food today?' => 'eye_healthy_food',
        'Did you perform blinking, focusing, or rolling eye exercises at least once today?' => 'eye_exercises',
        'Was your non-school screen time less than 2 hours today?' => 'screen_time_limit',
    ];
@endphp

@foreach($dailyQuizReport as $report)
    <div class="mb-5">
        <h5 class="text-primary">Report Date: {{ \Carbon\Carbon::parse($report->created_at)->format('d M, Y') }}</h5>
        <p><strong>Total Points:</strong> {{ $report->total_points }} / 20</p>
        <p><strong>Percentage:</strong> {{ $report->percentage }}%</p>

        <table class="table table-bordered mb-4">
            <thead class="table-light">
                <tr>
                    <th class="text-start">Question</th>
                    <th class="text-start">Answer</th>
                </tr>
            </thead>
            <tbody>
                @foreach($questions as $question => $field)
                    <tr>
                        <td class="text-start align-middle">{{ $question }}</td>
                        <td class="text-start align-middle">{{ $report->$field == 5 ? 'Yes' : 'No' }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    </div>
@endforeach
@else
    <p class="text-muted text-center">No quiz reports available.</p>
@endif
