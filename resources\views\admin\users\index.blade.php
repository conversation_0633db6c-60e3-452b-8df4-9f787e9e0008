@extends("admin.layouts.app")

@section('content')
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div class="row right-card">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">Users List</h4>
                    <input type="search" class="form-control search ms-auto w-25" name="search" id="search" placeholder="Search here...">
                </div>
                @php
                $i = $userslists->firstItem();
                @endphp
                <div class="section-body">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_customers_table">
                                        <thead>
                                            <tr class="text-start text-gray-400 fw-bold fs-7 text-uppercase gs-0">
                                                <th class="w-10px pe-2">
                                                    <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                        <input class="form-check-input" type="checkbox" id="selectAll" />
                                                    </div>
                                                </th>
                                                <th class="w-10px pe-2">Sr.No</th>
                                                <th class="text-center">Image</th>
                                                <th>Parent Name</th>
                                                <th>Children Name</th>
                                                <th>Mobile</th>
                                                <th>Status</th>
                                                <th class="text-center">Created Date</th>
                                                <th class="text-center">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody class="fw-semibold text-gray-600">
                                            @if ($userslists->count() > 0)
                                                @foreach($userslists as $item)
                                                    @if($item->is_admin != 1)
                                                    <tr>
                                                        <td class="text-center">
                                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                <input class="form-check-input row-checkbox" type="checkbox" value="{{ $item->id }}" />
                                                            </div>
                                                        </td>
                                                        <td class="text-center">{{ $i }}</td>
                                                        <td class="text-center">
                                                            @if($item->user_image)
                                                                <img src="{{ asset($item->user_image) }}" alt="User Image" width="50" height="50">
                                                            @else
                                                                <img src="{{ asset('assets/img/eyenstein.png') }}" alt="Default Avatar" width="50" height="50">
                                                            @endif
                                                        </td>
                                                        <td>{{ $item->full_name ?? '' }}</td>
                                                        <td>
                                                            @foreach($item->children as $child)
                                                                {{ $child->name_of_kid }},<br>
                                                            @endforeach
                                                        </td>
                                                        <td>{{ $item->mobile ?? '' }}</td>
                                                        <td>
                                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                                <input type="checkbox" class="form-check-input status-checkbox" data-id="{{ $item->id }}" {{ $item->status == 1 ? 'checked' : '' }}>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">{{ $item->created_at ? \Carbon\Carbon::parse($item->created_at)->format('Y-m-d') : '' }}</td>
                                                        <td class="text-center">
                                                            <a href="{{ route('userView', ['id' => $item->id]) }}" title="View" class="me-2">
                                                                <i class="fi fi-rr-eye text-primary action-icon"></i>
                                                            </a>
                                                            <a href="{{ route('userEdit', ['id' => $item->id]) }}" title="Edit" class="me-2">
                                                                <i class="fi fi-rr-edit text-success action-icon"></i>
                                                            </a>
                                                            <a href="#" onclick="confirmDelete({{ $item->id }})" title="Delete">
                                                                <i class="fi fi-rr-trash text-danger action-icon"></i>
                                                            </a>
                                                            <form id="delete-form-{{ $item->id }}" action="{{ route('userdelete', ['id' => $item->id]) }}" method="POST" style="display: none;">
                                                                @csrf
                                                                @method('DELETE')
                                                            </form>
                                                        </td>
                                                    </tr>
                                                    @endif
                                                    @php $i++; @endphp
                                                @endforeach
                                            @else
                                                <tr>
                                                    <td colspan="8" class="text-center">No specific record found for this query.</td>
                                                </tr>
                                            @endif
                                        </tbody>
                                    </table>
                                    <div class="d-flex justify-content-center">
                                        {{ $userslists->links() }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    function confirmDelete(id) {
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to undo this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Submit the delete form
                document.getElementById(`delete-form-${id}`).submit();
            }
        });
    }
</script>
<script>
    $(document).ready(function() {
        // *******Status-Toggle-Script*******
        $('.status-checkbox').on('change', function() {
            var userId = $(this).data('id');
            var status = $(this).prop('checked') ? 1 : 0;

            $.ajax({
                url: "{{ route('toggleUserStatus') }}",
                type: "POST",
                data: {
                    _token: "{{ csrf_token() }}",
                    user_id: userId,
                    status: status
                },
                success: function(response) {
                    if (response.success) {
                        Swal.fire({
                            icon: 'success',
                            title: 'Updated!',
                            text: 'Status updated successfully.',
                            timer: 2000,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            icon: 'error',
                            title: 'Oops...',
                            text: 'Failed to update status.'
                        });
                    }
                },
                error: function() {
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Something went wrong. Please try again.'
                    });
                }
            });
        });
        // *******Status-Toggle-Script*******
    });
</script>
 <script>
    $(document).ready(function () {
        $('#selectAll').on('change', function () {
            $('.row-checkbox').prop('checked', $(this).prop('checked'));
        });
        function fetchData(searchQuery = '') {
            $.ajax({
                url: "{{ route('userIndex') }}",
                type: "GET",
                data: { search: searchQuery },
                success: function (data) {
                    $('tbody').html($(data).find('tbody').html());
                }
            });
        }

        $('#search').on('input', function () {
            let searchQuery = $(this).val();
            fetchData(searchQuery);
        });

        $('#search').on('search', function () {
            if ($(this).val() === '') {
                fetchData();
            }
        });
    });
</script>
@endsection