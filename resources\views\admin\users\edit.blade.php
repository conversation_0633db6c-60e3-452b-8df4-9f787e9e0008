@extends("admin.layouts.app")

@section('content')
@php
    // Convert the time to 24-hour format if it exists
    $timeOfBirth = isset($user->time_of_birth) ? date('H:i', strtotime($user->time_of_birth)) : '';
@endphp
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit User Profile</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('userIndex') }}" class="text-muted text-hover-primary">User List</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">Edit User</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                <div class="loader-container" style="display: none;">
                    <div class="loader"></div>
                </div>

                <div class="d-flex flex-column flex-lg-row">
                    <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
                        <div class="card">
                            <div class="card-body p-12">
                                <form id="createUserForm" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT') <!-- Adding POST method for update -->
                                    <input type="hidden" name="id" value="{{ $user->id }}">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row">
                                                <!-- User Name Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="full_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control form-control-solid" id="full_name" name="full_name" value="{{ old('full_name', $user->full_name) }}" placeholder="Enter user name">
                                                    <div class="text-danger mt-2 error-message" data-field="full_name"></div>
                                                </div>

                                                <!-- Mobile Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="mobile" class="form-label">Phone</label>
                                                    <input type="tel" class="form-control form-control-solid" id="mobile" value="{{ old('mobile', $user->mobile) }}" name="mobile">
                                                    <div class="text-danger mt-2 error-message" data-field="mobile"></div>
                                                </div>
                                                
                                                <!-- Email Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                                                    <input type="email" class="form-control form-control-solid" id="email" name="email" value="{{ old('email', $user->email) }}" placeholder="Enter email address">
                                                    <div class="text-danger mt-2 error-message" data-field="email"></div>
                                                </div>

                                                <!-- Occupation Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="occupation" class="form-label">Occupation <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control form-control-solid" id="occupation" name="occupation" value="{{ old('occupation', $user->occupation) }}" placeholder="Enter occupation">
                                                    <div class="text-danger mt-2 error-message" data-field="occupation"></div>
                                                </div>

                                                <!-- Status Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                    <select class="form-control form-control-solid" id="status" name="status">
                                                        <option value="" disabled {{ old('status', $user->status) ? '' : 'selected' }}>Select Status</option>
                                                        <option value="1" {{ old('status', $user->status) == 'active' ? 'selected' : '' }}>Active</option>
                                                        <option value="0" {{ old('status', $user->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                    </select>
                                                    <div class="text-danger mt-2 error-message" data-field="status"></div>
                                                </div>



                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-end mt-4">
                                        <button type="submit" id="submitUserForm" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ensure correct script placement -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        $('#createUserForm').submit(function(e) {
            e.preventDefault(); 
            $('.loader-container').show();
            let formData = $(this).serialize(); 
            $('.error-message').text(''); 

            $.ajax({
                url: "{{ route('userUpdate', $user->id) }}", 
                type: "POST",
                data: formData, 
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') 
                },
                success: function(response) {
                    $('.loader-container').hide();
                    if (response.success) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'User updated successfully!',
                            icon: 'success',
                            timer: 1000,
                            showConfirmButton: false
                        }).then(() => {
                            window.location.href = "{{ route('userIndex') }}";
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Unexpected response from the server.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr) {
                    $('.loader-container').hide();
                    if (xhr.status === 422) { 
                        let errors = xhr.responseJSON.errors;
                        for (let field in errors) {
                            let errorMessage = errors[field][0];
                            $(`.error-message[data-field="${field}"]`).text(errorMessage || 'Required').show();
                        }
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred while updating the user. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                complete: function() {
                    $('.loader-container').hide();
                }
            });
        });

        $('.loader-container').hide();
    });

    // Remove error message when user types or selects value
    $('input, select').on('input change', function () {
        let fieldName = $(this).attr('name');
        $(`.error-message[data-field="${fieldName}"]`).text('').hide();
    });
</script>
@endsection