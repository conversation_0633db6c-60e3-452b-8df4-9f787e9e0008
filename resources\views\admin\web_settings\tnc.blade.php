@extends("admin.layouts.app")

@section('content')
<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Web Settings - Terms & Conditions</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('termsNcondition') }}" class="text-muted text-hover-primary">Terms & Conditions</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">Edit</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                <div class="loader-container" style="display: none;">
                    <div class="loader"></div>
                </div>

                <div class="d-flex flex-column flex-lg-row">
                    <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
                        <div class="card">
                            <div class="card-body">
                                <form id="tncForm" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT') <!-- Adding POST method for update -->
                                    <input type="hidden" name="id" value="{{ $tnc->id }}">
                                    <div class="card">
                                        <div class="card-body">
                                            <div class="row">
                                                <input type="hidden" class="form-control form-control-solid" id="type" name="type" value="terms_conditions" placeholder="terms_conditions" readonly>
                                                <div class="col-sm-12">
                                                    <textarea class="form-control form-control-solid editor" id="values" name="values">{{ $tnc->value }}</textarea>
                                                    <div class="text-danger mt-2 error-message" data-field="values"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end mt-4">
                                        <button type="submit" id="submitForm" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ensure correct script placement -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.ckeditor.com/4.20.2/standard/ckeditor.js"></script>
<script>
    CKEDITOR.replace('values');
    $(document).ready(function () {
        $('#tncForm').submit(function (e) {
            e.preventDefault();
            $('.loader-container').show();
            $('.error-message').text('');

            const id = $('input[name="id"]').val();
            const url = `/admin/web-setting/update/${id}`;

            const formData = {
                _token: '{{ csrf_token() }}',
                _method: 'PUT',
                type: $('input[name="type"]').val(),
                values: CKEDITOR.instances['values'].getData()
            };

            $.ajax({
                url: url,
                type: "POST",
                data: formData,
                success: function (response) {
                    $('.loader-container').hide();
                    if (response.success) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'Terms & Conditions updated successfully!',
                            icon: 'success',
                            timer: 1500,
                            showConfirmButton: false
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Unexpected error occurred.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function (xhr) {
                    $('.loader-container').hide();
                    if (xhr.status === 422) {
                        let errors = xhr.responseJSON.errors;
                        for (let field in errors) {
                            let message = errors[field][0];
                            $(`.error-message[data-field="${field}"]`).text(message).show();
                        }
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Server error occurred. Please try again later.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                }
            });
        });

        $('input, textarea').on('input change', function () {
            let fieldName = $(this).attr('name');
            $(`.error-message[data-field="${fieldName}"]`).text('').hide();
        });
    });
</script>
@endsection