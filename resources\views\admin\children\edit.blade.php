@extends("admin.layouts.app")

@section('content')

<div class="app-wrapper flex-column flex-row-fluid" id="kt_app_wrapper">
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="app-container container-xxl">
                {{-- Page-Title-And-Breadcrumb --}}
                <div class="page-title d-flex flex-column justify-content-center flex-wrap mt-5 mb-5">
                    <h1 class="page-heading d-flex text-dark fw-bold fs-3 flex-column justify-content-center my-0">Edit User Profile</h1>
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                        <li class="breadcrumb-item text-muted"><a href="{{ route('children.index', ['user_id' => $children->user_id, 'child_id' => $children->id]) }}" class="text-muted text-hover-primary">User List</a></li>
                        <li class="breadcrumb-item"><span class="bullet bg-gray-400 w-5px h-2px"></span></li>
                        <li class="breadcrumb-item text-muted">Edit User</li>
                    </ul>
                </div>
                {{-- Page-Title-And-Breadcrumb --}}

                <div class="loader-container" style="display: none;">
                    <div class="loader"></div>
                </div>

                <div class="d-flex flex-column flex-lg-row">
                    <div class="flex-lg-row-fluid mb-10 mb-lg-0 me-lg-7 me-xl-10">
                        <div class="card">
                            <div class="card-body p-12">
                                <form id="createUserForm" method="POST" enctype="multipart/form-data">
                                    @csrf
                                    @method('PUT') <!-- Adding POST method for update -->
                                    <input type="hidden" name="id" value="{{ $children->id }}">
                                    <div class="card mb-3">
                                        <div class="card-body">
                                            <div class="row">
                                                <!-- User Name Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="name_of_kid" class="form-label">Full Name <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control form-control-solid" id="name_of_kid" name="name_of_kid" value="{{ old('name_of_kid', $children->name_of_kid) }}" placeholder="Enter Kid name">
                                                    <div class="text-danger mt-2 error-message" data-field="name_of_kid"></div>
                                                </div>

                                                <!-- Age Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="age" class="form-label">Age <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control form-control-solid" id="age" name="age" value="{{ old('age', $children->age) }}" placeholder="Enter Your Age ">
                                                    <div class="text-danger mt-2 error-message" data-field="age"></div>
                                                </div>

                                                <!-- School Name Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="school" class="form-label">School Name</label>
                                                    <input type="text" class="form-control form-control-solid" id="school" value="{{ old('school', $children->school) }}" name="school">
                                                    <div class="text-danger mt-2 error-message" data-field="school"></div>
                                                </div>
                                                
                                                <!-- Existing Eye Power Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="existing_eye_power" class="form-label">Existing Eye Power <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control form-control-solid" id="existing_eye_power" name="existing_eye_power" value="{{ old('existing_eye_power', $children->existing_eye_power) }}" placeholder="Enter Existing Eye Power">
                                                    <div class="text-danger mt-2 error-message" data-field="existing_eye_power"></div>
                                                </div>
                                                <!-- Status Field -->
                                                <div class="col-sm-6 mb-3">
                                                    <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                                                    <select class="form-control form-control-solid" id="status" name="status">
                                                        <option value="" disabled {{ old('status', $children->status) ? '' : 'selected' }}>Select Status</option>
                                                        <option value="1" {{ old('status', $children->status) == 'active' ? 'selected' : '' }}>Active</option>
                                                        <option value="0" {{ old('status', $children->status) == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                                    </select>
                                                    <div class="text-danger mt-2 error-message" data-field="status"></div>
                                                </div>
                                                
                                                <!-- Kid Image Field -->
                                               {{-- <div class="col-sm-6 mb-3">
                                                   <label for="kid_image" class="form-label">Image <span class="text-danger">*</span></label>
                                       
                                                   @if(!empty($children->kid_image))
                                                       <div class="mb-2">
                                                           <img src="{{ asset('path/to/kid_images/' . $children->kid_image) }}" alt="Kid Image" width="100" height="100">
                                                       </div>
                                                   @endif
                                                   <input type="file" class="form-control" id="kid_image" name="kid_image">
                                                   <div class="text-danger mt-2 error-message" data-field="kid_image"></div>
                                               </div> --}}
                                               


                                            </div>
                                        </div>
                                    </div>

                                    <div class="text-end mt-4">
                                        <button type="submit" id="submitUserForm" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ensure correct script placement -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
    $(document).ready(function() {
        $('#createUserForm').submit(function(e) {
            e.preventDefault(); 
            $('.loader-container').show();
            let formData = $(this).serialize(); 
            $('.error-message').text(''); 

            $.ajax({
                url: "{{ route('children.update', ['user_id' => $children->user_id, 'child_id' => $children->id]) }}", 
                type: "POST",
                data: formData, 
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') 
                },
                success: function(response) {
                    $('.loader-container').hide();
                    if (response.success) {
                        Swal.fire({
                            title: 'Success!',
                            text: 'User updated successfully!',
                            icon: 'success',
                            timer: 1000,
                            showConfirmButton: false
                        }).then(() => {
                            window.location.href = "{{ route('userView', ['id' => $children->user_id]) }}";
                        });
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'Unexpected response from the server.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                error: function(xhr) {
                    $('.loader-container').hide();
                    if (xhr.status === 422) { 
                        let errors = xhr.responseJSON.errors;
                        for (let field in errors) {
                            let errorMessage = errors[field][0];
                            $(`.error-message[data-field="${field}"]`).text(errorMessage || 'Required').show();
                        }
                    } else {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred while updating the user. Please try again.',
                            icon: 'error',
                            confirmButtonText: 'OK'
                        });
                    }
                },
                complete: function() {
                    $('.loader-container').hide();
                }
            });
        });

        $('.loader-container').hide();
    });

    // Remove error message when user types or selects value
    $('input, select').on('input change', function () {
        let fieldName = $(this).attr('name');
        $(`.error-message[data-field="${fieldName}"]`).text('').hide();
    });
</script>
@endsection