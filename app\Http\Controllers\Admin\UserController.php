<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;

class UserController extends Controller
{
    //User Index
    public function userIndex(Request $request)
    {
        try {
            $query = User::with('children');
            // Search
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where('full_name', 'LIKE', "%{$search}%")
                    ->orWhere('email', 'LIKE', "%{$search}%")
                    ->orWhere('mobile', 'LIKE', "%{$search}%")
                    ->orWhere('occupation', 'LIKE', "%{$search}%")
                    ->orWhere('created_at', 'LIKE', "%{$search}%")
                    ->orWhereHas('children', function ($childQuery) use ($search) {
                        $childQuery->where('name_of_kid', 'LIKE', "%{$search}%");
                    });
            }
            $userslists = $query->orderBy('created_at', 'desc')->simplePaginate(10);
            return view('admin.users.index', compact('userslists'));
        } catch (Exception $e) {
            Log::error('Error fetching Users list: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again later.');
        }
    }

    // *********User-Status-Change*********
    public function toggleUserStatus(Request $request)
    {
        try {
            $request->validate([
                'user_id' => 'required|exists:users,id',
                'status' => 'required',
            ]);

            $user = User::findOrFail($request->user_id);
            $user->status = $request->status;
            $user->save();

            return response()->json(['success' => true, 'message' => 'Status updated successfully.']);
        } catch (Exception $e) {
            Log::error('Error updating user status: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Something went wrong. Please try again.'], 500);
        }
    }
    // *********User-Status-Change*********

    public function userView($id)
    {
        try {
            $users = User::with(['children'])->findOrFail($id);

            return view('admin.users.view', compact('users'));
        } catch (Exception $e) {
            Log::error('Error fetching users details: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again later.');
        }
    }

    //Edit User
    public function userEdit($id)
    {
        try {
            $user = User::with(['children'])->where('id', $id)->first();

            return view('admin.users.edit', compact('user'));
        } catch (Exception $e) {
            Log::error('Error fetching user details for editing: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Something went wrong. Please try again later.');
        }
    }

    // Update User

    public function userUpdate(Request $request, $id)
    {
        try {
            $user = User::findOrFail($id);

            $request->validate([
                'full_name' => 'required|string|max:255',
                'mobile' => 'nullable|string',
                'email' => 'required|email|unique:users,email,' . $user->id,
                'occupation' => 'required|string',
                'user_image' => 'nullable',
                'status' => 'required',
            ]);

            // Update the user record
            $user->update([
                'full_name' => $request->input('full_name'),
                'mobile' => $request->input('mobile'),
                'email' => $request->input('email'),
                'occupation' => $request->input('occupation'),
                'user_image' => $request->input('user_image'),
                'status' => $request->input('status')
            ]);

            return response()->json(['success' => true, 'message' => 'User updated successfully!'], 200);
        } catch (ValidationException $e) {
            Log::error('Error updating user: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Validation failed!',
                'errors' => $e->errors(),
            ], 422);
        } catch (ModelNotFoundException $e) {
            Log::error('Error updating user: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'User not found!',
            ], 404);
        } catch (Exception $e) {
            Log::error('Error updating user: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Something went wrong. Please try again later.',
            ], 500);
        }
    }

    public function userDelete($id)
    {
        try {
            $user = User::findOrFail($id);
            $user->delete();
            return redirect()->route('userIndex')->with('success', 'User deleted successfully!');
        } catch (Exception $e) {
            Log::error('Error deleting user: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Failed to delete user. Please try again later.');
        }
    }
}
