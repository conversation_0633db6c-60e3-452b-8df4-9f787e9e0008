-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 04, 2025 at 07:52 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `eye_sight_admin`
--

-- --------------------------------------------------------

--
-- Table structure for table `admins`
--

CREATE TABLE `admins` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `profile_image` text DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `admin_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1=>admin',
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1=>Active, 0=>Inactive',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admins`
--

INSERT INTO `admins` (`id`, `name`, `email`, `password`, `profile_image`, `phone`, `admin_type`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Mr. Eyenstein Rodrigues', '<EMAIL>', '$2y$12$VM4BAgc396dQLzK4CBT56uDboSONI8kPkn1mZNgnms.R8gUgY0AjO', NULL, '+919988776655', 1, 1, '2025-05-02 10:18:51', '2025-05-02 10:18:51', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `badges`
--

CREATE TABLE `badges` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `required_days` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `childrens`
--

CREATE TABLE `childrens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `name_of_kid` varchar(255) NOT NULL,
  `kid_image` varchar(255) DEFAULT NULL,
  `age` int(11) NOT NULL,
  `school` varchar(255) DEFAULT NULL,
  `existing_eye_power` varchar(255) DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1=>Active, 0=>Inactive',
  `isIntakeSubmitted` tinyint(20) NOT NULL DEFAULT 0,
  `deleted_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `childrens`
--

INSERT INTO `childrens` (`id`, `user_id`, `name_of_kid`, `kid_image`, `age`, `school`, `existing_eye_power`, `status`, `isIntakeSubmitted`, `deleted_at`, `created_at`, `updated_at`) VALUES
(1, 1, 'Shehnshah Jaisawal', NULL, 15, 'The Jain International School', '1.5', 1, 0, NULL, '2025-05-02 04:53:56', '2025-05-02 04:53:56'),
(2, 1, 'Guluna Jaiswal', NULL, 10, 'Brilliant public school', '2.5', 1, 0, NULL, '2025-07-07 11:34:41', '2025-07-07 11:34:41');

-- --------------------------------------------------------

--
-- Table structure for table `children_documents`
--

CREATE TABLE `children_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `child_id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `file_path` varchar(255) DEFAULT NULL,
  `uploaded_date` varchar(255) DEFAULT NULL,
  `file_type` varchar(255) DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `children_documents`
--

INSERT INTO `children_documents` (`id`, `child_id`, `user_id`, `name`, `file_path`, `uploaded_date`, `file_type`, `status`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 1, 1, 'children_documents/682582a7d5cdd.pdf', 'https://firebasestorage.googleapis.com/v0/b/eyefit-217d9.firebasestorage.app/o/children_documents%2F682582a7d5cdd.pdf?alt=media', '2025-05-15', 'pdf', 1, '2025-05-15 00:29:05', '2025-05-15 00:51:09', NULL),
(2, 1, 1, 'children_documents/6835970f48cf0.pdf', 'https://firebasestorage.googleapis.com/v0/b/eyefit-217d9.firebasestorage.app/o/children_documents%2F6835970f48cf0.pdf?alt=media', '2025-05-27', 'pdf', 1, '2025-05-27 05:12:25', '2025-05-27 05:12:35', '2025-05-27 05:12:35');

-- --------------------------------------------------------

--
-- Table structure for table `child_badges`
--

CREATE TABLE `child_badges` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `child_id` bigint(20) UNSIGNED NOT NULL,
  `badge_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `daily_eye_health_reports`
--

CREATE TABLE `daily_eye_health_reports` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `child_id` bigint(20) UNSIGNED NOT NULL,
  `outdoor_activity` int(11) NOT NULL,
  `eye_healthy_food` int(11) NOT NULL,
  `eye_exercises` int(11) NOT NULL,
  `screen_time_limit` int(11) NOT NULL,
  `total_points` int(11) DEFAULT NULL,
  `percentage` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `daily_eye_health_reports`
--

INSERT INTO `daily_eye_health_reports` (`id`, `user_id`, `child_id`, `outdoor_activity`, `eye_healthy_food`, `eye_exercises`, `screen_time_limit`, `total_points`, `percentage`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 5, 5, 5, 5, 20, 100.00, '2025-05-02 05:32:18', '2025-05-02 05:32:18'),
(2, 1, 1, 5, 5, 5, 5, 20, 100.00, '2025-05-05 00:39:52', '2025-05-05 00:39:52'),
(3, 1, 1, 0, 0, 5, 5, 10, 50.00, '2025-05-06 00:42:11', '2025-05-06 00:42:11'),
(4, 1, 1, 5, 5, 5, 5, 20, 100.00, '2025-05-07 00:32:26', '2025-05-07 00:32:26'),
(5, 1, 1, 0, 0, 0, 5, 5, 25.00, '2025-05-08 00:49:07', '2025-05-08 00:49:07');

--
-- Triggers `daily_eye_health_reports`
--
DELIMITER $$
CREATE TRIGGER `before_eye_health_insert` BEFORE INSERT ON `daily_eye_health_reports` FOR EACH ROW BEGIN
                DECLARE total INT DEFAULT 0;
                SET total =
                    IFNULL(NEW.outdoor_activity, 0) +
                    IFNULL(NEW.eye_healthy_food, 0) +
                    IFNULL(NEW.eye_exercises, 0) +
                    IFNULL(NEW.screen_time_limit, 0);

                SET NEW.total_points = total;
                SET NEW.percentage = (total / 20) * 100;
            END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `before_eye_health_update` BEFORE UPDATE ON `daily_eye_health_reports` FOR EACH ROW BEGIN
                DECLARE total INT DEFAULT 0;
                SET total =
                    IFNULL(NEW.outdoor_activity, 0) +
                    IFNULL(NEW.eye_healthy_food, 0) +
                    IFNULL(NEW.eye_exercises, 0) +
                    IFNULL(NEW.screen_time_limit, 0);

                SET NEW.total_points = total;
                SET NEW.percentage = (total / 20) * 100;
            END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `google_sheet_data`
--

CREATE TABLE `google_sheet_data` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `full_name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '2014_10_12_000000_create_users_table', 1),
(2, '2014_10_12_100000_create_password_reset_tokens_table', 1),
(3, '2019_08_19_000000_create_failed_jobs_table', 1),
(4, '2019_12_14_000001_create_personal_access_tokens_table', 1),
(5, '2025_03_31_083122_create_admins_table', 1),
(6, '2025_03_31_083647_create_childrens_table', 1),
(7, '2025_04_02_052724_create_myopia_risk_assessments_table', 1),
(8, '2025_04_02_060156_create_daily_eye_health_reports_table', 1),
(9, '2025_04_02_074719_create_badges_table', 1),
(10, '2025_04_02_074801_create_child_badges_table', 1),
(11, '2025_04_24_073100_create_web_settings_table', 1),
(12, '2025_05_06_072633_create_google_sheet_data_table', 2),
(13, '2025_05_14_065833_create_children_documents_table', 3),
(15, '2025_05_19_102801_create_send_notifications_table', 4);

-- --------------------------------------------------------

--
-- Table structure for table `myopia_risk_assessments`
--

CREATE TABLE `myopia_risk_assessments` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `user_id` bigint(20) UNSIGNED NOT NULL,
  `child_id` bigint(20) UNSIGNED NOT NULL,
  `question_1` tinyint(4) NOT NULL DEFAULT 0,
  `question_2` tinyint(4) NOT NULL DEFAULT 0,
  `question_3` tinyint(4) NOT NULL DEFAULT 0,
  `question_4` tinyint(4) NOT NULL DEFAULT 0,
  `question_5` tinyint(4) NOT NULL DEFAULT 0,
  `question_6` tinyint(4) NOT NULL DEFAULT 0,
  `question_7` tinyint(4) NOT NULL DEFAULT 0,
  `question_8` tinyint(4) NOT NULL DEFAULT 0,
  `question_9` tinyint(4) NOT NULL DEFAULT 0,
  `question_10` tinyint(4) NOT NULL DEFAULT 0,
  `total_points` int(11) NOT NULL DEFAULT 0,
  `risk_level` varchar(10) NOT NULL DEFAULT 'Mild',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `myopia_risk_assessments`
--

INSERT INTO `myopia_risk_assessments` (`id`, `user_id`, `child_id`, `question_1`, `question_2`, `question_3`, `question_4`, `question_5`, `question_6`, `question_7`, `question_8`, `question_9`, `question_10`, `total_points`, `risk_level`, `created_at`, `updated_at`) VALUES
(1, 1, 1, 2, 0, 2, 2, 0, 2, 2, 0, 2, 0, 12, 'Severe', '2025-05-02 04:54:10', '2025-05-02 04:54:10');

--
-- Triggers `myopia_risk_assessments`
--
DELIMITER $$
CREATE TRIGGER `calculate_myopia_risk_insert` BEFORE INSERT ON `myopia_risk_assessments` FOR EACH ROW BEGIN
                SET NEW.total_points =
                    NEW.question_1 + NEW.question_2 + NEW.question_3 + NEW.question_4 + NEW.question_5 +
                    NEW.question_6 + NEW.question_7 + NEW.question_8 + NEW.question_9 + NEW.question_10;

                IF NEW.total_points BETWEEN 0 AND 4 THEN
                    SET NEW.risk_level = 'Mild';
                ELSEIF NEW.total_points BETWEEN 6 AND 10 THEN
                    SET NEW.risk_level = 'Moderate';
                ELSE
                    SET NEW.risk_level = 'Severe';
                END IF;
            END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `calculate_myopia_risk_update` BEFORE UPDATE ON `myopia_risk_assessments` FOR EACH ROW BEGIN
                SET NEW.total_points =
                    NEW.question_1 + NEW.question_2 + NEW.question_3 + NEW.question_4 + NEW.question_5 +
                    NEW.question_6 + NEW.question_7 + NEW.question_8 + NEW.question_9 + NEW.question_10;

                IF NEW.total_points BETWEEN 0 AND 4 THEN
                    SET NEW.risk_level = 'Mild';
                ELSEIF NEW.total_points BETWEEN 6 AND 10 THEN
                    SET NEW.risk_level = 'Moderate';
                ELSE
                    SET NEW.risk_level = 'Severe';
                END IF;
            END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `send_notifications`
--

CREATE TABLE `send_notifications` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `selected_users` text DEFAULT NULL,
  `users` varchar(255) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `message` text DEFAULT NULL,
  `img_url` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1=>sent, 2=>read',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `full_name` varchar(255) DEFAULT NULL,
  `mobile` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `occupation` varchar(255) DEFAULT NULL,
  `user_image` text DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `firebase_id` text DEFAULT NULL,
  `fcm_id` text DEFAULT NULL,
  `api_token` text DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 1 COMMENT '1=>active, 0=>inactive',
  `login_type` varchar(255) NOT NULL DEFAULT '1' COMMENT '1=>mobile_number, 2=>o-auth',
  `remember_token` varchar(100) DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `full_name`, `mobile`, `email`, `occupation`, `user_image`, `password`, `firebase_id`, `fcm_id`, `api_token`, `status`, `login_type`, `remember_token`, `email_verified_at`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Mayank Jaisawal', '+916266557879', '<EMAIL>', NULL, NULL, NULL, 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-WaJdrzf_1wBXfmIQlBDevM2XDEImviO8PpzuqaBqnmyZxd8dXTOTfoeSvrCPKnkdzHGJunoGgxxMjtfgl9B2ovieNAEv6O9pkue-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Wb4pQgChVwEsBm-0U3hIUDKaE7yfrNIpEvq-lfhtn0s', 1, '1', NULL, NULL, '2025-05-02 04:53:14', '2025-05-27 04:45:55', NULL),
(2, 'Selmon Bhoi', '+918876789878', '<EMAIL>', 'Actor', NULL, NULL, 'eyJhbGdfdfciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-susayushudgsdjhJYGEYTHABSwjDhdbHJEHEnDBD-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Tpadnyud29Uz_twbew4Wl3PjZ7mhSdyD9_jmDa0YRIw', 1, '1', NULL, NULL, '2025-05-19 04:44:52', '2025-05-19 04:44:52', NULL),
(3, 'Abhishek William', '+919039405729', '<EMAIL>', 'Musician', NULL, NULL, 'eyJhbGdfdfciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-susayushudgsdjhJYGEYTHABSwjDhdbHJEHEnDBD-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.J7GcUen9azG9NYAGvntEb60GxOU7V9OOR7ihouRlcL8', 1, '1', NULL, NULL, '2025-05-19 04:45:19', '2025-05-19 04:45:19', NULL),
(4, 'Ramesh Fivestar', '+918876789445', '<EMAIL>', 'Actor', NULL, NULL, 'eyJhbGdfdfciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-susayushudgsdjhJYGEYTHABSwjDhdbHJEHEnDBD-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.bN_m2YJNNLjq2osdoWio67wEAohY7CW5HTqu-20AxVY', 1, '1', NULL, NULL, '2025-05-19 04:46:11', '2025-05-19 04:46:11', NULL),
(5, 'Suresh Fivestar', '+919986789445', '<EMAIL>', 'Actor', NULL, NULL, 'eyJhbGdfdfciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-susayushudgsdjhJYGEYTHABSwjDhdbHJEHEnDBD-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ryIk-P4rw1K5LpCrO24xxqSeaNrUO7Cz3pd5tREQHjQ', 1, '1', NULL, NULL, '2025-05-19 04:46:26', '2025-05-19 04:46:26', NULL),
(6, 'Tinku Badmash', '+919986783344', '<EMAIL>', 'Contractor', NULL, NULL, 'eyJhbGdfdfciOiJSUzI1NiIsImtpZCI6IjkwOTg1NzhjNDg4MWRjMDVlYmYxOWExNWJhMjJkOGZkMWFiMzRjOGEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.fG2S7zYRR0fkwfaNdaLN3Tcj2VrdfnF1luSb_hsWXl4aeBgXbYWazmPz6gS7gtopeU1gRvn3ImCazhkdv0cTFEFQaL0HIkGKpjgrWalKsq9xwHvbPNCtnZlFSPyd6xDEsR3Ozj1Ug3BzwA04CD79TUTuagG-hzl7U-susayushudgsdjhJYGEYTHABSwjDhdbHJEHEnDBD-DIkAhBRtMqszX8k8xiQLK8NpVdR4eBf55ePFD5n1DZq6eLSLFXayLhaarudx1I-z3mGkrGnDjPcomp0g', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.FGu7R7SGGcW51hCMtHlyu9MTo12IanhPxL4jq1rOgwQ', 1, '1', NULL, NULL, '2025-05-19 04:47:15', '2025-05-19 04:47:15', NULL),
(8, 'Test Image', '+919876543210', '<EMAIL>', 'Actor', 'https://firebasestorage.googleapis.com/v0/b/eyefit-217d9.firebasestorage.app/o/user_image%2F682d70105aae9.jpg?alt=media', NULL, 'eyJhbGciOiJSUzI1NiIsImtpZCI6IjY3ZDhjZWU0ZTYwYmYwMzYxNmM1ODg4NTJiMjA5MTZkNjRjMzRmYmEiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.EcttnYlDb2rH8mienpi7bi4fOD0mCYhg5wlm4fw7i1Kum9AMZywGCkgDFJYXx6sTopQsztRTajvXJ_tUnU5judLlkWHZx6t9dfh8mK6UZsXUG5Dn7C-JgxElHzwCp3erl7j7pYexWuNWYLAvOKVxiiU0CLSAnTZ01KB7NDA4NByakN00ZxBD_7vIFb1a_C0ge3XcD9kFs3XaTvLBH-yRiUM8pZV8ws6vCKjQcAQxY7KpLAsiHyUlgX8bRi45STMvNfFiH8GONtgugr2VZwIewW2bKbo_RG7AIHWlOV5_AdXVTNTXcK_McnXmZFMvyxPRZmeWa7PKktCRatHTXk4fAA', NULL, 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************._5IUQ1sGOU3X1oSP62_OmCb3T_oDHr0hnxwpjtxhDq4', 1, '1', NULL, NULL, '2025-05-21 00:47:54', '2025-05-21 00:47:54', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `web_settings`
--

CREATE TABLE `web_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `type` varchar(255) NOT NULL,
  `value` text NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `web_settings`
--

INSERT INTO `web_settings` (`id`, `type`, `value`, `created_at`, `updated_at`) VALUES
(1, 'terms_conditions', '<p>&nbsp;Please read these Terms and Conditions (\"Terms\", \"Terms and Conditions\") carefully before using our app. By accessing or using our services, you agree to be bound by these Terms.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;1. Acceptance of Terms&lt;/p&gt;</h2><p>By creating an account or using our services, you agree to comply with these Terms and all applicable laws and regulations.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;2. Use of Services&lt;/p&gt;</h2><p>- You agree not to use the app for any unlawful or unauthorized purposes.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;3. Intellectual Property Rights&lt;/p&gt;</h2><p>All content, features, and functionality of the app, including text, graphics, and logos, are the exclusive property of our company and are protected by copyright, trademark, and other intellectual property laws.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;4. User Accounts&lt;/p&gt;</h2><p>- You are responsible for maintaining the confidentiality of your account and password.<br>- We reserve the right to suspend or terminate your account for any violations of these Terms.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;5. Limitation of Liability&lt;/p&gt;</h2><p>We are not liable for any indirect, incidental, or consequential damages arising out of your use of the app or inability to access the app.</p><h2>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;6. Changes to Terms&lt;/p&gt;</h2><p>We reserve the right to update or modify these Terms at any time without prior notice. By continuing to use the app, you agree to the revised Terms.</p><p>If you have any questions about this Privacy Policy, please contact our support team.</p>', '2025-04-24 07:32:06', '2025-04-24 07:32:06'),
(2, 'privacy_policy', '<p>We value your privacy and are committed to protecting your personal information. This Privacy Policy outlines how we collect, use, and safeguard your data.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;1. Information We Collect&lt;/p&gt;</h2>\n\n<p>- Personal details such as name, email, and phone number etc.<br />\n- Activity data, including interactions with our app and preferences.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;2. How We Use Your Information&lt;/p&gt;</h2>\n\n<p>- To improve app functionality and provide a personalized experience.<br />\n- To communicate updates, news, events notifications , or new features.<br />\n- To ensure compliance with applicable laws and regulations.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;3. Sharing Your Information&lt;/p&gt;</h2>\n\n<p>We do not sell your data. Your information is shared only with trusted partners to improve our services or comply with legal requirements.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;4. Data Security&lt;/p&gt;</h2>\n\n<p>We employ industry-standard security measures to protect your data against unauthorized access, alteration, or loss.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;5. Your Rights&lt;/p&gt;</h2>\n\n<p>- Request deletion of your personal data.<br />\n- Opt out of non-essential data collection.</p>\n\n<h2>&lt;p style=&quot;padding:0; margin:0; color:#E55E2A;&quot;&gt;6. Changes to this Policy&lt;/p&gt;</h2>\n\n<p>This Privacy Policy may be updated occasionally to reflect changes in our practices. We encourage you to review it periodically.</p>\n\n<p>If you have any questions about this Privacy Policy, please contact our support team.</p>', '2025-04-24 07:32:06', '2025-04-24 09:30:09'),
(3, 'contact_us', '<p>We’re here to help! If you have any questions, feedback, or need assistance, feel free to reach out to us through the following channels.</p><h2>Contact Methods<br>&lt;p style=\"padding:0; margin:0; color:#E55E2A;\"&gt;1. Email Support:&lt;/p&gt;</h2><p>- <EMAIL></p>', '2025-04-24 07:33:13', '2025-04-24 07:33:13');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admins`
--
ALTER TABLE `admins`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `admins_email_unique` (`email`);

--
-- Indexes for table `badges`
--
ALTER TABLE `badges`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `childrens`
--
ALTER TABLE `childrens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `childrens_user_id_foreign` (`user_id`);

--
-- Indexes for table `children_documents`
--
ALTER TABLE `children_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `children_documents_child_id_foreign` (`child_id`),
  ADD KEY `children_documents_user_id_foreign` (`user_id`);

--
-- Indexes for table `child_badges`
--
ALTER TABLE `child_badges`
  ADD PRIMARY KEY (`id`),
  ADD KEY `child_badges_child_id_foreign` (`child_id`),
  ADD KEY `child_badges_badge_id_foreign` (`badge_id`);

--
-- Indexes for table `daily_eye_health_reports`
--
ALTER TABLE `daily_eye_health_reports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `daily_eye_health_reports_user_id_foreign` (`user_id`),
  ADD KEY `daily_eye_health_reports_child_id_foreign` (`child_id`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `google_sheet_data`
--
ALTER TABLE `google_sheet_data`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `myopia_risk_assessments`
--
ALTER TABLE `myopia_risk_assessments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `myopia_risk_assessments_user_id_foreign` (`user_id`),
  ADD KEY `myopia_risk_assessments_child_id_foreign` (`child_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `send_notifications`
--
ALTER TABLE `send_notifications`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`);

--
-- Indexes for table `web_settings`
--
ALTER TABLE `web_settings`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admins`
--
ALTER TABLE `admins`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `badges`
--
ALTER TABLE `badges`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `childrens`
--
ALTER TABLE `childrens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `children_documents`
--
ALTER TABLE `children_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `child_badges`
--
ALTER TABLE `child_badges`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `daily_eye_health_reports`
--
ALTER TABLE `daily_eye_health_reports`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `google_sheet_data`
--
ALTER TABLE `google_sheet_data`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `myopia_risk_assessments`
--
ALTER TABLE `myopia_risk_assessments`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `send_notifications`
--
ALTER TABLE `send_notifications`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `web_settings`
--
ALTER TABLE `web_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `childrens`
--
ALTER TABLE `childrens`
  ADD CONSTRAINT `childrens_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `children_documents`
--
ALTER TABLE `children_documents`
  ADD CONSTRAINT `children_documents_child_id_foreign` FOREIGN KEY (`child_id`) REFERENCES `childrens` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `children_documents_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `child_badges`
--
ALTER TABLE `child_badges`
  ADD CONSTRAINT `child_badges_badge_id_foreign` FOREIGN KEY (`badge_id`) REFERENCES `badges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `child_badges_child_id_foreign` FOREIGN KEY (`child_id`) REFERENCES `childrens` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `daily_eye_health_reports`
--
ALTER TABLE `daily_eye_health_reports`
  ADD CONSTRAINT `daily_eye_health_reports_child_id_foreign` FOREIGN KEY (`child_id`) REFERENCES `childrens` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `daily_eye_health_reports_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `myopia_risk_assessments`
--
ALTER TABLE `myopia_risk_assessments`
  ADD CONSTRAINT `myopia_risk_assessments_child_id_foreign` FOREIGN KEY (`child_id`) REFERENCES `childrens` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `myopia_risk_assessments_user_id_foreign` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
